package net.narutomod.yongheng;

import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

@SideOnly(Side.CLIENT)
public class ChakraRestoreEventHandler {
    private boolean wasPressed = false;

    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase == TickEvent.Phase.END && Minecraft.getMinecraft().player != null) {
            boolean isPressed = KeyBindingChakraRestore.CHAKRA_RESTORE.isKeyDown();

            if (isPressed != wasPressed) {
                wasPressed = isPressed;
                // 只在按键状态变化时输出日志
                System.out.println("查克拉恢复按键: " + (isPressed ? "按下" : "释放"));
                ProcedureChakraRestore.sendKeyPress(isPressed);
            }
        }
    }
}
