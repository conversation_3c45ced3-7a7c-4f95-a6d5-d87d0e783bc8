package net.narutomod.jiami;

import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.narutomod.ElementsNarutomodMod;

/**
 * 服务器许可证检查器 - 优化版
 * 只在服务器启动时进行一次性验证，不进行循环检查
 */
@ElementsNarutomodMod.ModElement.Tag
public class ServerLicenseChecker extends ElementsNarutomodMod.ModElement {

    private static boolean licenseValidated = false;
    private static boolean validationPassed = false;

    public ServerLicenseChecker(ElementsNarutomodMod instance) {
        super(instance, 1); // 最高优先级确保最早执行
    }

    @Override
    public void preInit(FMLPreInitializationEvent event) {
        // 预初始化阶段不进行验证，等到serverLoad阶段
        // 这样可以确保能正确检测到服务器类型
    }

    @Override
    public void serverLoad(FMLServerStartingEvent event) {
        // 检查是否为专用服务器，只有专用服务器才需要验证
        if (!event.getServer().isDedicatedServer()) {
            System.out.println("[NarutoMod] 单人游戏模式，跳过许可证验证");
            validationPassed = true; // 单人游戏默认通过
            licenseValidated = true;
            return;
        }

        // 专用服务器才进行验证
        if (!licenseValidated) {
            performLicenseValidation();
        }

        if (!validationPassed) {
            System.err.println("[NarutoMod] 许可证验证未通过，服务器将关闭！");
            KeyValidator.shutdownServer("许可证验证失败");
        } else {
            System.out.println("[NarutoMod] 许可证验证通过，服务器正常启动");
        }
    }

    /**
     * 执行许可证验证
     */
    private static synchronized void performLicenseValidation() {
        if (licenseValidated) {
            return; // 避免重复验证
        }

        System.out.println("[NarutoMod] ========================================");
        System.out.println("[NarutoMod] 开始服务器许可证验证...");
        System.out.println("[NarutoMod] 模组版本: 0.3.2-beta");
        System.out.println("[NarutoMod] ========================================");

        try {
            validationPassed = KeyValidator.validateServerKey();
            licenseValidated = true;

            if (validationPassed) {
                System.out.println("[NarutoMod]  许可证验证成功！");
                System.out.println("[NarutoMod]  服务器已授权运行此模组");
            } else {
                System.err.println("[NarutoMod]  许可证验证失败！");
                System.err.println("[NarutoMod]  服务器未授权运行此模组");
            }

        } catch (Exception e) {
            System.err.println("[NarutoMod]  许可证验证过程中发生异常: " + e.getMessage());
            validationPassed = false;
            licenseValidated = true;
        }

        System.out.println("[NarutoMod] ========================================");
    }

    /**
     * 获取验证状态 - 供其他组件查询使用
     */
    public static boolean isLicenseValid() {
        return licenseValidated && validationPassed;
    }

    /**
     * 获取验证是否已完成
     */
    public static boolean isValidationCompleted() {
        return licenseValidated;
    }
}