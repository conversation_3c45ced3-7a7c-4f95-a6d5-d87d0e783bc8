package net.narutomod.jiami;

import net.minecraftforge.fml.common.FMLCommonHandler;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.event.FMLServerStartingEvent;

/**
 * 许可证管理器 - 简化版
 * 只在服务器启动时进行一次性验证，无调试功能
 */
public class LicenseManager {

    private static boolean initialized = false;
    private static boolean serverMode = false;

    /**
     * 初始化许可证系统
     */
    public static void initialize(FMLPreInitializationEvent event) {
        if (initialized) {
            return;
        }

        initialized = true;

        // 检测运行环境
        serverMode = FMLCommonHandler.instance().getSide().isServer();

        System.out.println("[NarutoMod] 许可证系统初始化完成");
    }
    
    /**
     * 服务器启动时的许可证检查
     */
    public static void performServerValidation(FMLServerStartingEvent event) {
        if (!serverMode) {
            return;
        }

        // 检查是否为专用服务器
        if (!event.getServer().isDedicatedServer()) {
            return;
        }

        System.out.println("[NarutoMod] 开始服务器许可证验证...");

        // 执行验证
        boolean isValid = KeyValidator.validateServerKey();

        if (!isValid) {
            System.err.println("[NarutoMod] 许可证验证失败！");
            System.err.println("[NarutoMod] 请联系模组作者QQ1340198249获取正确的许可证密钥");

            // 关闭服务器
            KeyValidator.shutdownServer("许可证验证失败");
        } else {
            System.out.println("[NarutoMod] 许可证验证通过，服务器启动成功");
        }
    }
    
    /**
     * 获取许可证状态
     */
    public static boolean isLicenseValid() {
        return ServerLicenseChecker.isLicenseValid();
    }

    /**
     * 获取验证状态
     */
    public static boolean isValidationCompleted() {
        return ServerLicenseChecker.isValidationCompleted();
    }
}
