package net.narutomod.shuxing;

import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.util.ResourceLocation;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.common.capabilities.CapabilityManager;
import net.minecraftforge.event.AttachCapabilitiesEvent;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.narutomod.NarutomodMod;

public class AttributeManager {

    public static final String[] ATTRIBUTE_NAMES = {
        "火遁强化", "风遁强化", "水遁强化", "雷遁强化", "土遁强化",
        "体质强化", "轮回强化", "瞳力强化", "精神力强化"
    };

    public static final String[] ATTRIBUTE_DESCRIPTIONS = {
        "增加火遁忍术伤害",
        "增加风遁忍术伤害",
        "增加水遁忍术伤害",
        "增加雷遁忍术伤害",
        "增加土遁忍术伤害",
        "增加查克拉恢复速度",
        "死亡后保留更多查克拉", // 轮回强化描述
        "减少写轮眼等瞳术疲劳",
        "增加忍术蓄力速度" // 修改描述
    };

    private static int nextPacketId = 200; // 避免与其他包冲突

    public static void init() {
        // 注册Capability
        CapabilityManager.INSTANCE.register(AttributeEnhancement.class,
            new AttributeEnhancement.Storage(), AttributeEnhancement::new);

        // 注册网络包
        registerPackets();

        // 注册事件处理器
        MinecraftForge.EVENT_BUS.register(new AttributeManager());
        MinecraftForge.EVENT_BUS.register(new AttributeEventHandler());
    }

    private static void registerPackets() {
        NarutomodMod.PACKET_HANDLER.registerMessage(
            PacketAttributeEnhancement.Handler.class,
            PacketAttributeEnhancement.class,
            nextPacketId++,
            Side.SERVER
        );

        NarutomodMod.PACKET_HANDLER.registerMessage(
            PacketSyncAttributes.Handler.class,
            PacketSyncAttributes.class,
            nextPacketId++,
            Side.CLIENT
        );

        NarutomodMod.PACKET_HANDLER.registerMessage(
            PacketRequestSync.Handler.class,
            PacketRequestSync.class,
            nextPacketId++,
            Side.SERVER
        );
    }
    
    @SubscribeEvent
    public void onAttachCapabilities(AttachCapabilitiesEvent<Entity> event) {
        if (event.getObject() instanceof EntityPlayer) {
            event.addCapability(new ResourceLocation("narutomod", "attribute_enhancement"), 
                new AttributeEnhancement.Provider());
        }
    }
    
    @SubscribeEvent
    public void onPlayerClone(PlayerEvent.Clone event) {
        if (event.isWasDeath()) {
            AttributeEnhancement oldAttr = AttributeEnhancement.get(event.getOriginal());
            AttributeEnhancement newAttr = AttributeEnhancement.get(event.getEntityPlayer());
            
            if (oldAttr != null && newAttr != null) {
                // 复制属性数据
                newAttr.deserializeNBT(oldAttr.serializeNBT());
                
                // 获取死亡时保存的查克拉信息
                double retainedChakra = event.getOriginal().getEntityData().getDouble("DeathRetainedChakra");
                double retentionRatio = event.getOriginal().getEntityData().getDouble("DeathRetentionRatio");
                
                // 传递给新玩家
                event.getEntityPlayer().getEntityData().setDouble("DeathRetainedChakra", retainedChakra);
                event.getEntityPlayer().getEntityData().setDouble("DeathRetentionRatio", retentionRatio);
                
                // 立即同步到客户端
                if (event.getEntityPlayer() instanceof EntityPlayerMP) {
                    // 延迟同步，确保玩家完全加载
                    event.getEntityPlayer().world.getMinecraftServer().addScheduledTask(() -> {
                        newAttr.syncToClient((EntityPlayerMP) event.getEntityPlayer());
                    });
                }
            }
        }
    }
    
    @SubscribeEvent
    public void onPlayerRespawn(net.minecraftforge.fml.common.gameevent.PlayerEvent.PlayerRespawnEvent event) {
        if (!event.player.world.isRemote) {
            // 只有在死亡复活时才设置查克拉恢复标记，维度传送不设置
            // 注意：PlayerRespawnEvent在维度传送时也会触发，所以我们不在这里设置标记
            // 查克拉恢复标记已经在PlayerTracker的onClone方法中正确设置

            // 同步属性数据
            AttributeEnhancement attr = AttributeEnhancement.get(event.player);
            if (attr != null && event.player instanceof EntityPlayerMP) {
                attr.syncToClient((EntityPlayerMP) event.player);
            }

            System.out.println("玩家重生事件: " + event.player.getName() + " - 仅同步属性数据，不设置查克拉恢复标记");
        }
    }

    // 删除 restoreDeathChakra 方法，不再需要
    
    // 给玩家添加属性点的便捷方法
    public static void giveAttributePoints(EntityPlayer player, int points) {
        AttributeEnhancement attr = AttributeEnhancement.get(player);
        if (attr != null) {
            attr.addAvailablePoints(points);
        }
    }
    
    // 获取属性加成描述
    public static String getAttributeBonus(int attributeIndex, int level) {
        switch (attributeIndex) {
            case 0: case 1: case 2: case 3: case 4:
                return String.format("当前加成: +%.1f%% 伤害", level * 0.5);
            case 5:
                return String.format("当前加成: +%.1f%% 查克拉恢复", level * 2.0);
            case 6: // 轮回强化
                double retention = (0.1 + level * 0.005) * 100.0;
                return String.format("死亡保留: %.1f%% 查克拉", retention);
            case 7:
                return String.format("疲劳减少: %.1f%%", level * 1.5);
            case 8:
                return String.format("蓄力加速: +%.1f%%", level * 2.0); // 修改为2%每级
            default:
                return "未知效果";
        }
    }
    
    // 添加静态方法供其他系统调用
    public static float applyElementalDamageBonus(EntityPlayer player, float damage, String element) {
        AttributeEnhancement attr = AttributeEnhancement.get(player);
        if (attr != null) {
            double bonus = attr.getElementalDamageBonus(element);
            return (float)(damage * (1.0 + bonus));
        }
        return damage;
    }

    public static float applyChakraRegenBonus(EntityPlayer player, float baseRegen) {
        AttributeEnhancement attr = AttributeEnhancement.get(player);
        if (attr != null) {
            double bonus = attr.getChakraRegenBonus(); // 每级体质+2%
            return (float)(baseRegen * (1.0 + bonus));
        }
        return baseRegen;
    }

    // 通用的忍术蓄力速度加成方法
    public static float applyJutsuChargeSpeedBonus(EntityPlayer player, float baseChargeRate) {
        AttributeEnhancement attr = AttributeEnhancement.get(player);
        if (attr != null) {
            double bonus = attr.getJutsuChargeSpeedBonus(); // 每级精神力+2%
            return (float)(baseChargeRate * (1.0 + bonus));
        }
        return baseChargeRate;
    }

    // 为特定忍术类型应用蓄力加成
    public static double applyChargingBonus(EntityPlayer player, double baseValue, String jutsuType) {
        AttributeEnhancement attr = AttributeEnhancement.get(player);
        if (attr != null) {
            double bonus = attr.getJutsuChargeSpeedBonus();
            return baseValue * (1.0 + bonus);
        }
        return baseValue;
    }

    public static float applyEyeFatigueReduction(EntityPlayer player, float fatigue) {
        AttributeEnhancement attr = AttributeEnhancement.get(player);
        if (attr != null) {
            double reduction = attr.getEyeFatigueReduction();
            return (float)(fatigue * (1.0 - reduction));
        }
        return fatigue;
    }

    // 获取玩家查克拉信息的调试方法
    public static String getChakraDebugInfo(EntityPlayer player) {
        AttributeEnhancement attr = AttributeEnhancement.get(player);
        net.narutomod.Chakra.Pathway pathway = 
            net.narutomod.Chakra.pathway(player);
        
        if (attr != null && pathway != null) {
            double battleXp = net.narutomod.PlayerTracker.getBattleXp(player);
            double baseChakra = battleXp * 0.5d;
            double chakraLimit = attr.getChakraLimit();
            double currentChakra = pathway.getAmount();
            double maxChakra = pathway.getMax();
            int totalAttributes = attr.getTotalAllocatedPoints();
            int limitLevel = attr.getChakraLimitLevel();
            int pointsToNext = attr.getPointsToNextChakraLimit();
            
            return String.format(
                "查克拉系统信息:\n" +
                "战斗经验: %.0f\n" +
                "基础查克拉: %.0f\n" +
                "属性总等级: %d\n" +
                "上限等级: %d\n" +
                "查克拉上限: %.0f\n" +
                "实际最大: %.0f\n" +
                "当前查克拉: %.0f\n" +
                "下级还需: %d点",
                battleXp, baseChakra, totalAttributes, limitLevel, 
                chakraLimit, maxChakra, currentChakra, pointsToNext
            );
        }
        
        return "无法获取查克拉信息";
    }

    // 添加便捷方法来更新玩家查克拉上限
    public static void updatePlayerChakraLimit(EntityPlayer player) {
        if (!player.world.isRemote) {
            net.narutomod.Chakra.Pathway pathway = net.narutomod.Chakra.pathway(player);
            if (pathway instanceof net.narutomod.Chakra.PathwayPlayer) {
                ((net.narutomod.Chakra.PathwayPlayer) pathway).updateChakraLimit();
            }
        }
    }

    // 当战斗经验变化时调用此方法
    public static void onBattleXpChanged(EntityPlayer player) {
        if (!player.world.isRemote) {
            net.narutomod.Chakra.Pathway pathway = net.narutomod.Chakra.pathway(player);
            if (pathway instanceof net.narutomod.Chakra.PathwayPlayer) {
                ((net.narutomod.Chakra.PathwayPlayer) pathway).updateChakraLimit();
            }
        }
    }

    // 强制刷新玩家的所有相关数据
    public static void forceRefreshPlayer(EntityPlayer player) {
        if (!player.world.isRemote) {
            // 更新查克拉上限
            onBattleXpChanged(player);
            
            // 同步属性数据
            AttributeEnhancement attr = AttributeEnhancement.get(player);
            if (attr != null && player instanceof EntityPlayerMP) {
                attr.syncToClient((EntityPlayerMP) player);
            }
        }
    }
}
