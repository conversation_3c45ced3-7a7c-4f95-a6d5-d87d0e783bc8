package net.narutomod.shuxing;

import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.gui.GuiScreen;
import net.minecraft.client.Minecraft;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.text.TextFormatting;
import net.narutomod.NarutomodMod;
import net.narutomod.Chakra;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

@SideOnly(Side.CLIENT)
public class GuiAttributeEnhancement extends GuiScreen {
    private final EntityPlayer player;
    private AttributeEnhancement attributes;
    private GuiButton resetButton;
    private int syncRequestTicks = 0;
    private boolean initialSyncRequested = false;

    public GuiAttributeEnhancement(EntityPlayer player) {
        this.player = player;
        this.attributes = AttributeEnhancement.get(player);
    }

    @Override
    public void initGui() {
        super.initGui();

        // 刷新属性数据
        this.attributes = AttributeEnhancement.get(player);

        // 如果属性为空且在客户端，请求服务器同步
        if (this.attributes == null && player.world.isRemote && !initialSyncRequested) {
            // 发送同步请求包
            NarutomodMod.PACKET_HANDLER.sendToServer(new PacketRequestSync());
            initialSyncRequested = true;
            syncRequestTicks = 0;
        }
        
        // 更紧凑的GUI尺寸计算
        int attributeCount = AttributeManager.ATTRIBUTE_NAMES.length;
        int itemHeight = 20; // 减小行高
        int totalContentHeight = 200 + (attributeCount * itemHeight); // 基础高度 + 属性列表
        
        // 自适应尺寸，但设置合理的最小和最大值
        int guiWidth = Math.max(320, Math.min(380, this.width * 2 / 3));
        int guiHeight = Math.max(300, Math.min(totalContentHeight, this.height * 4 / 5));
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        
        // 计算实际可用的列表高度
        int availableListHeight = guiHeight - 180; // 为顶部和底部信息预留空间
        int actualItemHeight = Math.max(18, availableListHeight / attributeCount);
        int startY = centerY - (attributeCount * actualItemHeight) / 2 + 20;
        
        // 创建属性调整按钮 - 更紧凑的布局
        for (int i = 0; i < attributeCount; i++) {
            int y = startY + (i * actualItemHeight);
            // 减少按钮 - 左侧
            this.buttonList.add(new GuiButton(i * 2, centerX - 100, y, 20, 18, "-"));
            // 增加按钮 - 右侧
            this.buttonList.add(new GuiButton(i * 2 + 1, centerX + 80, y, 20, 18, "+"));
        }
        
        // 底部按钮区域 - 更紧凑
        int buttonAreaY = startY + (attributeCount * actualItemHeight) + 25;
        
        // 重置按钮 - 左侧
        this.resetButton = new GuiButton(100, centerX - 80, buttonAreaY, 75, 20, "重置");
        this.buttonList.add(resetButton);
        
        // 关闭按钮 - 右侧
        this.buttonList.add(new GuiButton(101, centerX + 5, buttonAreaY, 75, 20, "关闭"));
    }
    
    @Override
    protected void actionPerformed(GuiButton button) throws IOException {
        if (attributes == null) return;
        
        if (button.id == 100) {
            // 重置所有属性
            NarutomodMod.PACKET_HANDLER.sendToServer(new PacketAttributeEnhancement(-1, false));
        } else if (button.id == 101) {
            // 关闭GUI
            this.mc.displayGuiScreen(null);
        } else {
            // 属性调整
            int attributeIndex = button.id / 2;
            boolean isIncrease = (button.id % 2) == 1;
            
            // 检查是否可以执行操作
            if (isIncrease && attributes.canUpgrade(attributeIndex)) {
                NarutomodMod.PACKET_HANDLER.sendToServer(new PacketAttributeEnhancement(attributeIndex, true));
            } else if (!isIncrease && attributes.canDowngrade(attributeIndex)) {
                NarutomodMod.PACKET_HANDLER.sendToServer(new PacketAttributeEnhancement(attributeIndex, false));
            }
        }
    }
    
    @Override
    public void updateScreen() {
        super.updateScreen();

        // 定期刷新属性数据
        if (this.mc.world.getTotalWorldTime() % 20 == 0) {
            this.attributes = AttributeEnhancement.get(player);

            // 如果属性仍为空，重新请求同步
            if (this.attributes == null && player.world.isRemote) {
                syncRequestTicks++;
                if (syncRequestTicks > 60) { // 3秒后重试
                    NarutomodMod.PACKET_HANDLER.sendToServer(new PacketRequestSync());
                    syncRequestTicks = 0;
                }
            }
        }

        // 更新按钮状态
        if (attributes != null) {
            for (int i = 0; i < AttributeManager.ATTRIBUTE_NAMES.length; i++) {
                if (i * 2 + 1 < this.buttonList.size()) {
                    GuiButton decreaseBtn = this.buttonList.get(i * 2);
                    GuiButton increaseBtn = this.buttonList.get(i * 2 + 1);

                    // 更新按钮可用状态
                    decreaseBtn.enabled = attributes.canDowngrade(i);
                    increaseBtn.enabled = attributes.canUpgrade(i);
                }
            }

            // 更新重置按钮
            if (resetButton != null) {
                resetButton.enabled = attributes.getTotalAllocatedPoints() > 0;
            }
        }
    }
    
    @Override
    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        this.drawDefaultBackground();
        
        // 添加空值检查
        if (attributes == null) {
            String errorText = TextFormatting.RED + "属性数据加载中...";
            int errorWidth = fontRenderer.getStringWidth(errorText);
            this.drawString(fontRenderer, errorText, this.width / 2 - errorWidth / 2, this.height / 2, 0xFFFFFF);
            super.drawScreen(mouseX, mouseY, partialTicks);
            return;
        }
        
        // 确保 mc.player 不为空
        if (mc.player == null) {
            String errorText = TextFormatting.RED + "玩家数据未加载";
            int errorWidth = fontRenderer.getStringWidth(errorText);
            this.drawString(fontRenderer, errorText, this.width / 2 - errorWidth / 2, this.height / 2, 0xFFFFFF);
            super.drawScreen(mouseX, mouseY, partialTicks);
            return;
        }
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int attributeCount = AttributeManager.ATTRIBUTE_NAMES.length;
        
        // 自适应计算
        int totalContentHeight = 200 + (attributeCount * 20);
        int guiHeight = Math.max(300, Math.min(totalContentHeight, this.height * 4 / 5));
        int availableListHeight = guiHeight - 180;
        int itemHeight = Math.max(18, availableListHeight / attributeCount);
        int startY = centerY - (attributeCount * itemHeight) / 2 + 20;
        
        // === 顶部标题区域 ===
        String title = TextFormatting.GOLD + "属性强化";
        int titleWidth = fontRenderer.getStringWidth(title);
        this.drawString(fontRenderer, title, centerX - titleWidth / 2, startY - 80, 0xFFFFFF);
        
        // === 信息显示区域 ===
        int infoY = startY - 60;
        
        // 第一行：可用点数和总等级
        String pointsText = TextFormatting.GREEN + "可用: " + attributes.getAvailablePoints();
        String totalText = TextFormatting.AQUA + "总属性: " + attributes.getTotalAllocatedPoints();

        int totalWidth = fontRenderer.getStringWidth(totalText);

        this.drawString(fontRenderer, pointsText, centerX - 80, infoY, 0xFFFFFF);
        this.drawString(fontRenderer, totalText, centerX + 80 - totalWidth, infoY, 0xFFFFFF);
        
        // 第二行：查克拉上限阈值
        double chakraLimit = attributes.getChakraLimit();
        String limitText = TextFormatting.YELLOW + String.format("查克拉上限阈值: %.0f", chakraLimit);
        int limitWidth = fontRenderer.getStringWidth(limitText);
        this.drawString(fontRenderer, limitText, centerX - limitWidth / 2, infoY + 12, 0xFFFFFF);

        // 第三行：当前实际查克拉上限
        EntityPlayer player = mc.player;
        if (player != null) {
            double currentMax = Chakra.pathway(player).getMax();
            String currentText = TextFormatting.AQUA + String.format("当前查克拉上限: %.0f", currentMax);
            int currentWidth = fontRenderer.getStringWidth(currentText);
            this.drawString(fontRenderer, currentText, centerX - currentWidth / 2, infoY + 24, 0xFFFFFF);
        }

        // 第四行：提升信息
        int pointsToNext = attributes.getPointsToNextChakraLimit();
        String nextText = TextFormatting.GRAY + String.format("下级阈值需: %d点", pointsToNext);
        int nextWidth = fontRenderer.getStringWidth(nextText);
        this.drawString(fontRenderer, nextText, centerX - nextWidth / 2, infoY + 36, 0xFFFFFF);
        
        // === 分隔线 ===
        int lineY = startY - 15;
        this.drawHorizontalLine(centerX - 120, centerX + 120, lineY, 0xFF666666);
        
        // === 属性列表区域 ===
        for (int i = 0; i < attributeCount; i++) {
            int y = startY + (i * itemHeight);
            int level = attributes.getAttributeLevel(i);
            
            // 交替行背景色
            if (i % 2 == 0) {
                drawRect(centerX - 110, y - 1, centerX + 110, y + itemHeight - 1, 0x20FFFFFF);
            }
            
            // 属性名称 - 左对齐
            String shortName = getShortAttributeName(i);
            int nameColor = level == 0 ? 0xAAAAAA : (level == AttributeEnhancement.MAX_LEVEL ? 0xFFD700 : 0xFFFFFF);
            this.drawString(fontRenderer, shortName, centerX - 75, y + 3, nameColor);
            
            // 等级显示 - 中间
            String levelText = level + "/" + AttributeEnhancement.MAX_LEVEL;
            int levelWidth = fontRenderer.getStringWidth(levelText);
            this.drawString(fontRenderer, levelText, centerX - levelWidth / 2, y + 3, nameColor);
            
            // 进度条 - 右侧紧凑版
            int barX = centerX + 20;
            int barY = y + 5;
            int barWidth = 40;
            int barHeight = 6;
            
            // 背景
            drawRect(barX, barY, barX + barWidth, barY + barHeight, 0xFF333333);

            // 进度
            if (level > 0) {
                int progressWidth = (int)((double)level / AttributeEnhancement.MAX_LEVEL * barWidth);
                int progressColor = level == AttributeEnhancement.MAX_LEVEL ? 0xFFFFD700 : 0xFF00FF00;
                drawRect(barX, barY, barX + progressWidth, barY + barHeight, progressColor);
            }
            
            // 检查鼠标悬停显示详细信息
            if (mouseX >= centerX - 110 && mouseX <= centerX + 110 && mouseY >= y - 1 && mouseY <= y + itemHeight - 1) {
                List<String> tooltip = Arrays.asList(
                    TextFormatting.YELLOW + AttributeManager.ATTRIBUTE_NAMES[i],
                    TextFormatting.GRAY + AttributeManager.ATTRIBUTE_DESCRIPTIONS[i],
                    TextFormatting.GREEN + AttributeManager.getAttributeBonus(i, level)
                );
                
                // 为每个属性添加详细提示
                tooltip = new ArrayList<>(tooltip);

                if (i >= 0 && i <= 4) { // 五遁强化
                    tooltip.add(TextFormatting.DARK_GRAY + "基础伤害: 100%");
                    tooltip.add(TextFormatting.DARK_GRAY + "每级加成: +0.5%");
                    if (level < AttributeEnhancement.MAX_LEVEL) {
                        double nextLevel = (level + 1) * 0.5;
                        tooltip.add(TextFormatting.AQUA + String.format("下级: +%.1f%% 伤害", nextLevel));
                    }
                } else if (i == 5) { // 体质强化
                    tooltip.add(TextFormatting.DARK_GRAY + "基础恢复: 100%");
                    tooltip.add(TextFormatting.DARK_GRAY + "每级加成: +2%");
                    if (level < AttributeEnhancement.MAX_LEVEL) {
                        double nextLevel = (level + 1) * 2.0;
                        tooltip.add(TextFormatting.AQUA + String.format("下级: +%.1f%% 恢复", nextLevel));
                    }
                } else if (i == 6) { // 轮回强化
                    tooltip.add(TextFormatting.DARK_GRAY + "基础保留: 10%");
                    tooltip.add(TextFormatting.DARK_GRAY + "每级额外: +0.5%");
                    if (level < AttributeEnhancement.MAX_LEVEL) {
                        double nextLevel = (0.1 + (level + 1) * 0.005) * 100.0;
                        tooltip.add(TextFormatting.AQUA + String.format("下级: %.1f%%", nextLevel));
                    }
                } else if (i == 7) { // 瞳力强化
                    tooltip.add(TextFormatting.DARK_GRAY + "基础疲劳: 100%");
                    tooltip.add(TextFormatting.DARK_GRAY + "每级减少: -1.5%");
                    if (level < AttributeEnhancement.MAX_LEVEL) {
                        double nextLevel = (level + 1) * 1.5;
                        tooltip.add(TextFormatting.AQUA + String.format("下级: -%.1f%% 疲劳", nextLevel));
                    }
                } else if (i == 8) { // 精神力强化
                    tooltip.add(TextFormatting.DARK_GRAY + "基础蓄力: 100%");
                    tooltip.add(TextFormatting.DARK_GRAY + "每级加成: +2%");
                    if (level < AttributeEnhancement.MAX_LEVEL) {
                        double nextLevel = (level + 1) * 2.0;
                        tooltip.add(TextFormatting.AQUA + String.format("下级: +%.1f%% 蓄力速度", nextLevel));
                    }
                }
                
                this.drawHoveringText(tooltip, mouseX, mouseY);
            }
        }
        
        // === 底部说明 ===
        int hintY = startY + (attributeCount * itemHeight) + 8;
        String hintText1 = TextFormatting.DARK_GRAY + "属性强化只提高查克拉上限阈值";
        String hintText2 = TextFormatting.DARK_GRAY + "实际查克拉需通过战斗经验获得";
        
        int hint1Width = fontRenderer.getStringWidth(hintText1);
        int hint2Width = fontRenderer.getStringWidth(hintText2);
        
        this.drawString(fontRenderer, hintText1, centerX - hint1Width / 2, hintY, 0xFFFFFF);
        this.drawString(fontRenderer, hintText2, centerX - hint2Width / 2, hintY + 10, 0xFFFFFF);
        
        super.drawScreen(mouseX, mouseY, partialTicks);
    }
    
    @Override
    public boolean doesGuiPauseGame() {
        return false;
    }

    private String getShortAttributeName(int index) {
        String[] shortNames = {
            "火遁", "风遁", "水遁", "雷遁", "土遁",
            "体质", "轮回", "瞳力", "精神"
        };
        return index < shortNames.length ? shortNames[index] : "未知";
    }
}
