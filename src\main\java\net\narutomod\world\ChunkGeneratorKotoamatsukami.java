package net.narutomod.world;

import net.minecraft.world.World;
import net.minecraft.world.chunk.Chunk;
import net.minecraft.world.gen.IChunkGenerator;
import net.minecraft.world.gen.ChunkGeneratorFlat;
import net.minecraft.world.gen.FlatGeneratorInfo;
import net.minecraft.world.gen.FlatLayerInfo;
import net.minecraft.world.biome.Biome;
import net.minecraft.util.math.BlockPos;
import net.minecraft.entity.EnumCreatureType;
import net.minecraft.init.Blocks;
import net.minecraft.block.Block;

import java.util.List;
import java.util.ArrayList;

public class ChunkGenerator<PERSON>otoamatsukami implements IChunkGenerator {
	
	private final World world;
	private final long seed;
	private final ChunkGeneratorFlat chunkGenerator;
	
	public ChunkGeneratorKotoamatsukami(World world, long seed) {
		this.world = world;
		this.seed = seed;
		this.chunkGenerator = new ChunkGeneratorFlat(world, seed, false, createFlatGeneratorInfo().toString());
	}
	
	private FlatGeneratorInfo createFlatGeneratorInfo() {
		List<FlatLayerInfo> layers = new ArrayList<>();
		// 创建30层基岩的平坦世界
		layers.add(new FlatLayerInfo(30, Blocks.BEDROCK));
		
		FlatGeneratorInfo flatGeneratorInfo = new FlatGeneratorInfo();
		flatGeneratorInfo.getFlatLayers().addAll(layers);
		
		return flatGeneratorInfo;
	}
	
	@Override
	public Chunk generateChunk(int x, int z) {
		return this.chunkGenerator.generateChunk(x, z);
	}
	
	@Override
	public void populate(int chunkX, int chunkZ) {
		this.chunkGenerator.populate(chunkX, chunkZ);
	}
	
	@Override
	public List<Biome.SpawnListEntry> getPossibleCreatures(EnumCreatureType creatureType, BlockPos pos) {
		// 别天神世界中不生成生物
		return new ArrayList<>();
	}
	
	@Override
	public BlockPos getNearestStructurePos(World worldIn, String structureName, BlockPos position, boolean findUnexplored) {
		return this.chunkGenerator.getNearestStructurePos(worldIn, structureName, position, findUnexplored);
	}
	
	@Override
	public boolean isInsideStructure(World worldIn, String structureName, BlockPos pos) {
		return this.chunkGenerator.isInsideStructure(worldIn, structureName, pos);
	}
	
	@Override
	public void recreateStructures(Chunk chunkIn, int chunkX, int chunkZ) {
		this.chunkGenerator.recreateStructures(chunkIn, chunkX, chunkZ);
	}
	
	@Override
	public boolean generateStructures(Chunk chunkIn, int x, int z) {
		return this.chunkGenerator.generateStructures(chunkIn, x, z);
	}
}
