package net.narutomod.shuxing;

import io.netty.buffer.ByteBuf;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

public class PacketAttributeEnhancement implements IMessage {
    private int attributeIndex;
    private boolean isIncrease;
    
    public PacketAttributeEnhancement() {}
    
    public PacketAttributeEnhancement(int attributeIndex, boolean isIncrease) {
        this.attributeIndex = attributeIndex;
        this.isIncrease = isIncrease;
    }
    
    @Override
    public void fromBytes(ByteBuf buf) {
        this.attributeIndex = buf.readInt();
        this.isIncrease = buf.readBoolean();
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeInt(this.attributeIndex);
        buf.writeBoolean(this.isIncrease);
    }
    
    public static class Handler implements IMessageHandler<PacketAttributeEnhancement, IMessage> {
        @Override
        public IMessage onMessage(PacketAttributeEnhancement message, MessageContext ctx) {
            EntityPlayerMP player = ctx.getServerHandler().player;
            player.getServerWorld().addScheduledTask(() -> {
                AttributeEnhancement attr = AttributeEnhancement.get(player);
                if (attr != null) {
                    if (message.attributeIndex == -1) {
                        // 重置所有属性
                        attr.resetAllAttributesWithUpdate(player);
                    } else {
                        // 处理单个属性变化
                        if (message.isIncrease) {
                            attr.upgradeAttributeWithUpdate(message.attributeIndex, player);
                        } else {
                            attr.downgradeAttributeWithUpdate(message.attributeIndex, player);
                        }
                    }
                    // 同步到客户端
                    attr.syncToClient(player);
                }
            });
            return null;
        }
    }
}
