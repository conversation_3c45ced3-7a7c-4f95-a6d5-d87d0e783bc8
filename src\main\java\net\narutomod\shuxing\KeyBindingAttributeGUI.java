package net.narutomod.shuxing;

import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.client.registry.ClientRegistry;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import org.lwjgl.input.Keyboard;

@SideOnly(Side.CLIENT)
public class KeyBindingAttributeGUI {
    private static final KeyBinding ATTRIBUTE_GUI_KEY = new KeyBinding("key.attributegui.desc", Keyboard.KEY_K, "key.categories.narutomod");
    
    public static void register() {
        ClientRegistry.registerKeyBinding(ATTRIBUTE_GUI_KEY);
    }
    
    @SubscribeEvent
    @SideOnly(Side.CLIENT)
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        if (ATTRIBUTE_GUI_KEY.isPressed()) {
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.currentScreen == null && mc.player != null) {
                mc.displayGuiScreen(new GuiAttributeEnhancement(mc.player));
            }
        }
    }
}