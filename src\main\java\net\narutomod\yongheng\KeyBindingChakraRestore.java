package net.narutomod.yongheng;

import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.client.registry.ClientRegistry;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import org.lwjgl.input.Keyboard;

@SideOnly(Side.CLIENT)
public class KeyBindingChakraRestore {
    public static final KeyBinding CHAKRA_RESTORE = new KeyBinding("key.chakra_restore", Keyboard.KEY_R, "key.categories.narutomod");

    public static void register() {
        ClientRegistry.registerKeyBinding(CHAKRA_RESTORE);
    }

    // 移除重复的事件处理，让ChakraRestoreEventHandler统一处理
}
