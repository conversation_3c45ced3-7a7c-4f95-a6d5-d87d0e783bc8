package net.narutomod.shuxing;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.nbt.NBTBase;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.util.EnumFacing;
import net.minecraftforge.common.capabilities.Capability;
import net.minecraftforge.common.capabilities.CapabilityInject;
import net.minecraftforge.common.capabilities.ICapabilitySerializable;
import net.minecraftforge.common.util.INBTSerializable;

public class AttributeEnhancement implements INBTSerializable<NBTTagCompound> {
    @CapabilityInject(AttributeEnhancement.class)
    public static final Capability<AttributeEnhancement> CAPABILITY = null;
    
    public static final int MAX_LEVEL = 100;
    
    private int katonLevel = 0;
    private int futonLevel = 0;
    private int suitonLevel = 0;
    private int raitonLevel = 0;
    private int dotonLevel = 0;
    private int constitutionLevel = 0;
    private int reincarnationLevel = 0;
    private int eyePowerLevel = 0;
    private int mentalPowerLevel = 0;
    private int availablePoints = 0;
    
    // Getters
    public int getKatonLevel() { return katonLevel; }
    public int getFutonLevel() { return futonLevel; }
    public int getSuitonLevel() { return suitonLevel; }
    public int getRaitonLevel() { return raitonLevel; }
    public int getDotonLevel() { return dotonLevel; }
    public int getConstitutionLevel() { return constitutionLevel; }
    public int getReincarnationLevel() { return reincarnationLevel; }
    public int getEyePowerLevel() { return eyePowerLevel; }
    public int getMentalPowerLevel() { return mentalPowerLevel; }
    public int getAvailablePoints() { return availablePoints; }
    
    // 获取指定索引的属性等级
    public int getAttributeLevel(int index) {
        switch (index) {
            case 0: return katonLevel;
            case 1: return futonLevel;
            case 2: return suitonLevel;
            case 3: return raitonLevel;
            case 4: return dotonLevel;
            case 5: return constitutionLevel;
            case 6: return reincarnationLevel;
            case 7: return eyePowerLevel;
            case 8: return mentalPowerLevel;
            default: return 0;
        }
    }
    
    // 属性点操作
    public void addAvailablePoints(int points) {
        this.availablePoints += points;
    }
    
    public boolean canUpgrade(int attributeIndex) {
        return availablePoints > 0 && getAttributeLevel(attributeIndex) < MAX_LEVEL;
    }
    
    public boolean canDowngrade(int attributeIndex) {
        return getAttributeLevel(attributeIndex) > 0;
    }
    
    public boolean upgradeAttribute(int attributeIndex) {
        if (!canUpgrade(attributeIndex)) return false;
        
        switch (attributeIndex) {
            case 0: katonLevel++; break;
            case 1: futonLevel++; break;
            case 2: suitonLevel++; break;
            case 3: raitonLevel++; break;
            case 4: dotonLevel++; break;
            case 5: constitutionLevel++; break;
            case 6: reincarnationLevel++; break;
            case 7: eyePowerLevel++; break;
            case 8: mentalPowerLevel++; break;
            default: return false;
        }
        availablePoints--;
        return true;
    }
    
    public boolean downgradeAttribute(int attributeIndex) {
        if (!canDowngrade(attributeIndex)) return false;
        
        switch (attributeIndex) {
            case 0: katonLevel--; break;
            case 1: futonLevel--; break;
            case 2: suitonLevel--; break;
            case 3: raitonLevel--; break;
            case 4: dotonLevel--; break;
            case 5: constitutionLevel--; break;
            case 6: reincarnationLevel--; break;
            case 7: eyePowerLevel--; break;
            case 8: mentalPowerLevel--; break;
            default: return false;
        }
        availablePoints++;
        return true;
    }
    
    // 重置所有属性
    public void resetAllAttributes() {
        int totalPoints = getTotalAllocatedPoints();
        katonLevel = futonLevel = suitonLevel = raitonLevel = dotonLevel = 0;
        constitutionLevel = reincarnationLevel = eyePowerLevel = mentalPowerLevel = 0;
        availablePoints += totalPoints;
    }
    
    public int getTotalAllocatedPoints() {
        return katonLevel + futonLevel + suitonLevel + raitonLevel + dotonLevel + 
               constitutionLevel + reincarnationLevel + eyePowerLevel + mentalPowerLevel;
    }

    // 查克拉相关方法（确保只有这些，没有重复）
    public double getChakraLimit() {
        double baseLimit = 20000.0;
        int totalLevel = getTotalAllocatedPoints();
        int bonusLevels = totalLevel / 15;
        return baseLimit + (bonusLevels * 10000.0);
    }

    public int getChakraLimitLevel() {
        return getTotalAllocatedPoints() / 15;
    }

    public int getPointsToNextChakraLimit() {
        int currentPoints = getTotalAllocatedPoints();
        int nextThreshold = ((currentPoints / 15) + 1) * 15;
        return nextThreshold - currentPoints;
    }
    
    // 计算加成方法
    public double getElementalDamageBonus(String element) {
        switch(element.toLowerCase()) {
            case "katon": return katonLevel * 0.005;
            case "futon": return futonLevel * 0.005;
            case "suiton": return suitonLevel * 0.005;
            case "raiton": return raitonLevel * 0.005;
            case "doton": return dotonLevel * 0.005;
            default: return 0.0;
        }
    }
    
    public double getChakraRegenBonus() {
        return constitutionLevel * 0.02; // 每级体质强化增加2%查克拉恢复
    }
    
    // 轮回强化：死亡后查克拉保留
    public double getDeathChakraBonus() {
        return 0.1 + (reincarnationLevel * 0.005); // 基础10% + 每级轮回强化0.5%
    }

    // 获取死亡后保留的查克拉百分比（用于显示）
    public double getDeathChakraRetentionPercent() {
        return getDeathChakraBonus() * 100.0; // 转换为百分比显示
    }

    // 计算死亡后应该保留的查克拉量
    public double calculateRetainedChakra(double currentChakra, double maxChakra) {
        double retentionRatio = getDeathChakraBonus();
        double retainedAmount = currentChakra * retentionRatio;
        
        // 确保不超过最大查克拉值
        return Math.min(retainedAmount, maxChakra);
    }
    
    public double getEyeFatigueReduction() {
        return mentalPowerLevel * 0.015;
    }
    
    public double getJutsuChargeSpeedBonus() {
        return mentalPowerLevel * 0.02; // 每级增加2%蓄力速度
    }

    public double getJutsuSpeedBonus() {
        return getJutsuChargeSpeedBonus();
    }

    // 当属性发生变化时，通知查克拉系统更新
    private void notifyChakraUpdate(EntityPlayer player) {
        if (player != null && !player.world.isRemote) {
            net.narutomod.Chakra.Pathway pathway = net.narutomod.Chakra.pathway(player);
            if (pathway instanceof net.narutomod.Chakra.PathwayPlayer) {
                ((net.narutomod.Chakra.PathwayPlayer) pathway).updateChakraLimit();
            } else if (pathway != null) {
                // 对于非玩家路径，通过消耗0查克拉来触发更新
                pathway.consume(0.0);
            }
        }
    }

    // 重写属性升级方法，添加查克拉更新通知
    public boolean upgradeAttributeWithUpdate(int attributeIndex, EntityPlayer player) {
        boolean result = upgradeAttribute(attributeIndex);
        if (result && player != null) {
            notifyChakraUpdate(player);
        }
        return result;
    }

    // 重写属性降级方法，添加查克拉更新通知
    public boolean downgradeAttributeWithUpdate(int attributeIndex, EntityPlayer player) {
        boolean result = downgradeAttribute(attributeIndex);
        if (result && player != null) {
            notifyChakraUpdate(player);
        }
        return result;
    }

    // 重写重置方法，添加查克拉更新通知
    public void resetAllAttributesWithUpdate(EntityPlayer player) {
        resetAllAttributes();
        if (player != null) {
            notifyChakraUpdate(player);
        }
    }

    // 获取当前玩家的辅助方法
    private EntityPlayer getCurrentPlayer() {
        // 这里需要根据实际情况获取当前玩家
        // 可能需要在AttributeEnhancement中存储玩家引用
        return null; // 临时返回null，需要根据实际架构调整
    }
    
    @Override
    public NBTTagCompound serializeNBT() {
        NBTTagCompound nbt = new NBTTagCompound();
        nbt.setInteger("katon", katonLevel);
        nbt.setInteger("futon", futonLevel);
        nbt.setInteger("suiton", suitonLevel);
        nbt.setInteger("raiton", raitonLevel);
        nbt.setInteger("doton", dotonLevel);
        nbt.setInteger("constitution", constitutionLevel);
        nbt.setInteger("reincarnation", reincarnationLevel);
        nbt.setInteger("eyePower", eyePowerLevel);
        nbt.setInteger("mentalPower", mentalPowerLevel);
        nbt.setInteger("availablePoints", availablePoints);
        return nbt;
    }
    
    @Override
    public void deserializeNBT(NBTTagCompound nbt) {
        katonLevel = nbt.getInteger("katon");
        futonLevel = nbt.getInteger("futon");
        suitonLevel = nbt.getInteger("suiton");
        raitonLevel = nbt.getInteger("raiton");
        dotonLevel = nbt.getInteger("doton");
        constitutionLevel = nbt.getInteger("constitution");
        reincarnationLevel = nbt.getInteger("reincarnation");
        eyePowerLevel = nbt.getInteger("eyePower");
        mentalPowerLevel = nbt.getInteger("mentalPower");
        availablePoints = nbt.getInteger("availablePoints");
    }
    
    public static AttributeEnhancement get(EntityPlayer player) {
        return player.getCapability(CAPABILITY, null);
    }
    
    public static class Storage implements Capability.IStorage<AttributeEnhancement> {
        @Override
        public NBTBase writeNBT(Capability<AttributeEnhancement> capability, AttributeEnhancement instance, EnumFacing side) {
            return instance.serializeNBT();
        }
        
        @Override
        public void readNBT(Capability<AttributeEnhancement> capability, AttributeEnhancement instance, EnumFacing side, NBTBase nbt) {
            instance.deserializeNBT((NBTTagCompound) nbt);
        }
    }
    
    public static class Provider implements ICapabilitySerializable<NBTTagCompound> {
        private AttributeEnhancement instance = new AttributeEnhancement();
        
        @Override
        public boolean hasCapability(Capability<?> capability, EnumFacing facing) {
            return capability == CAPABILITY;
        }
        
        @Override
        public <T> T getCapability(Capability<T> capability, EnumFacing facing) {
            return capability == CAPABILITY ? CAPABILITY.cast(instance) : null;
        }
        
        @Override
        public NBTTagCompound serializeNBT() {
            return instance.serializeNBT();
        }
        
        @Override
        public void deserializeNBT(NBTTagCompound nbt) {
            instance.deserializeNBT(nbt);
        }
    }

    // 添加setter方法用于客户端同步
    public void setAvailablePoints(int points) {
        this.availablePoints = points;
    }

    public void setAttributeLevel(int index, int level) {
        if (level < 0 || level > MAX_LEVEL) return;

        switch (index) {
            case 0: katonLevel = level; break;
            case 1: futonLevel = level; break;
            case 2: suitonLevel = level; break;
            case 3: raitonLevel = level; break;
            case 4: dotonLevel = level; break;
            case 5: constitutionLevel = level; break;
            case 6: reincarnationLevel = level; break;
            case 7: eyePowerLevel = level; break;
            case 8: mentalPowerLevel = level; break;
        }
    }

    // 添加同步方法
    public void syncToClient(net.minecraft.entity.player.EntityPlayerMP player) {
        if (!player.world.isRemote) {
            try {
                int[] levels = new int[AttributeManager.ATTRIBUTE_NAMES.length];
                for (int i = 0; i < levels.length; i++) {
                    levels[i] = getAttributeLevel(i);
                }

                // 使用NarutomodMod的网络处理器
                net.narutomod.NarutomodMod.PACKET_HANDLER.sendTo(
                    new PacketSyncAttributes(this.availablePoints, levels),
                    player
                );
            } catch (Exception e) {
                System.err.println("属性同步失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
}
