package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.Chakra;
import net.narutomod.world.WorldKotoamatsukamiDimension;
import net.narutomod.util.CustomTeleporter;

import net.minecraft.world.World;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.ResourceLocation;
import net.minecraft.potion.PotionEffect;
import net.minecraft.init.MobEffects;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.item.ItemStack;

import java.util.Map;
import java.util.HashMap;
import java.util.UUID;
import java.util.Random;
import java.util.Optional;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureK<PERSON>amatsuka<PERSON> extends ElementsNarutomodMod.ModElement {

	// 别天神状态追踪
	public static Map<UUID, String> kotoamatsukamiCoords = new HashMap<>();
	public static final String COOLDOWN_KEY = "kotoamatsukami_cd";

	// 别天神技能配置
	private static final double CHAKRA_COST = 3000.0D;
	private static final double MIN_CHAKRA_REQUIRED = 8000.0D;
	private static final int DURATION = 1200; // 60秒
	private static final int COOLDOWN = 24000; // 20分钟冷却（非常强力的技能）
	private static final int EYE_DAMAGE = 35; // 对眼睛造成伤害

	public ProcedureKotoamatsukami(ElementsNarutomodMod instance) {
		super(instance, 602);
	}

	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure ProcedureKotoamatsukami!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure ProcedureKotoamatsukami!");
			return;
		}
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure ProcedureKotoamatsukami!");
			return;
		}

		Entity entity = (Entity) dependencies.get("entity");
		World world = (World) dependencies.get("world");
		boolean is_pressed = (Boolean) dependencies.get("is_pressed");

		if (!(entity instanceof EntityLivingBase)) {
			return;
		}

		EntityLivingBase caster = (EntityLivingBase) entity;

		// 检查冷却时间（在按下时检查）
		if (is_pressed && entity instanceof EntityPlayer) {
			EntityPlayer player = (EntityPlayer) entity;
			double cooldown = player.getEntityData().getDouble(COOLDOWN_KEY);
			if (world.getTotalWorldTime() < cooldown) {
				long remainingTime = (long)((cooldown - world.getTotalWorldTime()) / 20);
				player.sendStatusMessage(new net.minecraft.util.text.TextComponentTranslation("chattext.cooldown.formatted", remainingTime), true);
				return;
			}
		}

		// 只在按下时执行，并且防止重复触发
		if (!is_pressed) {
			return;
		}

		// 防止重复触发 - 检查是否刚刚使用过
		if (entity instanceof EntityPlayer) {
			EntityPlayer player = (EntityPlayer) entity;
			long lastUse = player.getEntityData().getLong("kotoamatsukami_last_use");
			if (world.getTotalWorldTime() - lastUse < 10) { // 0.5秒内防止重复
				return;
			}
			player.getEntityData().setLong("kotoamatsukami_last_use", world.getTotalWorldTime());
		}

		// 检查是否为玩家且满足条件
		if (entity instanceof EntityPlayer) {
			EntityPlayer player = (EntityPlayer) entity;
			
			// 检查头盔
			ItemStack helmet = player.inventory.armorInventory.get(3);
			if (!helmet.isEmpty() && helmet.hasTagCompound()) {
				net.minecraft.nbt.NBTTagCompound tag = helmet.getTagCompound();
				if (tag != null && tag.getBoolean("sharingan_blinded")) {
					player.sendMessage(new TextComponentString(TextFormatting.RED + "写轮眼已失明，无法使用别天神！"));
					return;
				}
			}

			// 检查查克拉上限
			if (Chakra.pathway(player).getMax() < MIN_CHAKRA_REQUIRED) {
				player.sendMessage(new TextComponentString(TextFormatting.RED + "需要至少 " + (int)MIN_CHAKRA_REQUIRED + " 查克拉上限才能使用别天神！"));
				return;
			}

			// 检查战斗经验 - 移除此检查，简化实现

			// 冷却检查已在前面处理

			// 检查查克拉
			if (!Chakra.pathway(player).consume(CHAKRA_COST)) {
				player.sendMessage(new TextComponentString(TextFormatting.RED + "查克拉不足！需要 " + (int)CHAKRA_COST + " 查克拉"));
				return;
			}
		}

		// 寻找目标
		RayTraceResult trace = ProcedureUtils.objectEntityLookingAt(entity, 3.0D); // 近距离技能
		EntityLivingBase target = null;

		if (trace.typeOfHit == RayTraceResult.Type.ENTITY && trace.entityHit instanceof EntityLivingBase) {
			target = (EntityLivingBase) trace.entityHit;
		}

		if (target == null) {
			if (entity instanceof EntityPlayer) {
				((EntityPlayer) entity).sendMessage(new TextComponentString(TextFormatting.RED + "未找到有效目标！别天神需要近距离施展"));
			}
			return;
		}

		// 执行别天神
		executeKotoamatsukami(caster, target, world);
	}

	private static void executeKotoamatsukami(EntityLivingBase caster, EntityLivingBase target, World world) {
		// 设置冷却时间
		if (caster instanceof EntityPlayer) {
			caster.getEntityData().setDouble(COOLDOWN_KEY, world.getTotalWorldTime() + COOLDOWN);
			((EntityPlayer) caster).sendMessage(new TextComponentString(TextFormatting.GOLD + "别天神发动！"));
			
			// 对眼睛造成伤害
			ItemStack helmet = ((EntityPlayer) caster).inventory.armorInventory.get(3);
			if (!helmet.isEmpty()) {
				helmet.damageItem(EYE_DAMAGE, caster);
			}
		}

		// 记录原始坐标
		String coords = target.dimension + "," + target.posX + "," + target.posY + "," + target.posZ;
		target.getEntityData().setString("kotoamatsukami_coords", coords);

		// 清空目标查克拉
		if (target instanceof EntityPlayer) {
			Chakra.pathway(target).clear();
		}

		// 设置别天神时间
		target.getEntityData().setInteger("kotoamatsukami_time", DURATION);

		// 播放音效
		net.minecraft.util.SoundEvent soundEvent = net.minecraft.util.SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:kotoamatsukami"));
		if (soundEvent != null) {
			world.playSound(null, caster.posX, caster.posY, caster.posZ, soundEvent, SoundCategory.NEUTRAL, 1.0F, 1.0F);
		}

		// 消息已在executeKotoamatsukami中发送，避免重复

		// 传送目标到别天神世界（简化实现 - 在当前世界创建特殊效果）
		activateKotoamatsukami(target);
	}

	// 激活别天神效果 - 传送到别天神世界
	private static void activateKotoamatsukami(EntityLivingBase target) {
		if (target.world.isRemote) {
			return; // 只在服务器端执行
		}

		// 传送到别天神维度
		Random random = new Random();
		int kotoamatsukamiDimId = WorldKotoamatsukamiDimension.getDimensionId();

		Optional<Entity> newEntity = CustomTeleporter.teleportToDimension(target, kotoamatsukamiDimId,
			random.nextInt(1000), 32.0D, random.nextInt(1000));

		if (!newEntity.isPresent()) {
			// 传送失败，应用本地效果作为备选
			target.addPotionEffect(new PotionEffect(MobEffects.GLOWING, DURATION, 0, false, true));
			target.addPotionEffect(new PotionEffect(MobEffects.SLOWNESS, DURATION, 2, false, false));
			target.addPotionEffect(new PotionEffect(MobEffects.WEAKNESS, DURATION, 2, false, false));
			target.addPotionEffect(new PotionEffect(MobEffects.NAUSEA, DURATION, 1, false, true));

			if (target instanceof EntityPlayer) {
				((EntityPlayer) target).sendMessage(new TextComponentString(TextFormatting.GOLD + "你被别天神控制了！"));
			}
			return;
		}

		// 传送成功，对新实体应用效果
		EntityLivingBase newTarget = (EntityLivingBase) newEntity.get();

		// 如果目标是怪物，让它停止攻击
		if (newTarget instanceof EntityMob) {
			EntityMob mob = (EntityMob) newTarget;
			mob.setAttackTarget(null);
			mob.setRevengeTarget(null);
		}

		// 给目标发送消息
		if (newTarget instanceof EntityPlayer) {
			((EntityPlayer) newTarget).sendMessage(new TextComponentString(TextFormatting.GOLD + "你陷入了别天神幻境！"));
		}

		// 设置持续伤害标记
		newTarget.getEntityData().setBoolean("kotoamatsukami_damage", true);
		newTarget.getEntityData().setFloat("kotoamatsukami_max_health", newTarget.getMaxHealth());

		// 创建玩家分身（简化实现）
		createPlayerClones(newTarget);
	}

	// 创建玩家分身（简化版本）
	private static void createPlayerClones(EntityLivingBase target) {
		// 这里可以添加创建分身的逻辑
		// 由于复杂性，暂时省略分身创建
	}

	// 清除别天神效果 - 传送回原世界
	public static void clearKotoamatsukami(EntityLivingBase target) {
		if (target.getEntityData().hasKey("kotoamatsukami_time")) {
			// 获取原始坐标
			String coords = target.getEntityData().getString("kotoamatsukami_coords");

			// 清除数据
			target.getEntityData().removeTag("kotoamatsukami_time");
			target.getEntityData().removeTag("kotoamatsukami_coords");
			target.getEntityData().removeTag("kotoamatsukami_damage");
			target.getEntityData().removeTag("kotoamatsukami_max_health");

			// 移除相关药水效果
			target.removePotionEffect(MobEffects.GLOWING);
			target.removePotionEffect(MobEffects.SLOWNESS);
			target.removePotionEffect(MobEffects.WEAKNESS);
			target.removePotionEffect(MobEffects.NAUSEA);

			// 如果在别天神世界，传送回原世界
			if (target.world.provider.getDimension() == WorldKotoamatsukamiDimension.getDimensionId()) {
				Optional<Entity> newEntity = CustomTeleporter.teleportBack(target, coords);
				if (newEntity.isPresent() && newEntity.get() instanceof EntityPlayer) {
					((EntityPlayer) newEntity.get()).sendMessage(new TextComponentString(TextFormatting.GREEN + "别天神效果已解除，你回到了现实！"));
				}
			} else {
				if (target instanceof EntityPlayer) {
					((EntityPlayer) target).sendMessage(new TextComponentString(TextFormatting.GREEN + "别天神效果已解除！"));
				}
			}
		}
	}

	// 检查并清理过期的别天神效果
	public static void checkKotoamatsukamiExpiry(EntityLivingBase entity, World world) {
		if (entity.getEntityData().hasKey("kotoamatsukami_time")) {
			int time = entity.getEntityData().getInteger("kotoamatsukami_time");
			if (time > 0) {
				entity.getEntityData().setInteger("kotoamatsukami_time", time - 1);

				// 在别天神世界中每20tick（1秒）造成最大生命值1%的伤害
				if (entity.world.provider.getDimension() == WorldKotoamatsukamiDimension.getDimensionId() &&
					entity.getEntityData().getBoolean("kotoamatsukami_damage") &&
					time % 20 == 0) {

					float maxHealth = entity.getEntityData().getFloat("kotoamatsukami_max_health");
					if (maxHealth <= 0) {
						maxHealth = entity.getMaxHealth();
						entity.getEntityData().setFloat("kotoamatsukami_max_health", maxHealth);
					}

					float damage = maxHealth * 0.01F; // 1%最大生命值
					entity.attackEntityFrom(net.minecraft.util.DamageSource.MAGIC, damage);

					// 给玩家发送伤害提示
					if (entity instanceof EntityPlayer && time % 100 == 0) { // 每5秒提示一次
						((EntityPlayer) entity).sendStatusMessage(new net.minecraft.util.text.TextComponentString(
							net.minecraft.util.text.TextFormatting.DARK_RED + "别天神幻境正在侵蚀你的生命力..."), true);
					}
				}
			} else {
				clearKotoamatsukami(entity);
			}
		}
	}
}
