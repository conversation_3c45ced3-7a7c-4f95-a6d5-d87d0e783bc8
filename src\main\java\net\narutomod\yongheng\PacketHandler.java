package net.narutomod.yongheng;

import net.minecraftforge.fml.common.network.NetworkRegistry;
import net.minecraftforge.fml.common.network.simpleimpl.SimpleNetworkWrapper;
import net.minecraftforge.fml.relauncher.Side;

public class PacketHandler {
    public static final SimpleNetworkWrapper INSTANCE = NetworkRegistry.INSTANCE.newSimpleChannel("yongheng");
    private static int nextID = 0;
    
    public static void registerMessages() {
        INSTANCE.registerMessage(
            ProcedureChakraRestore.ChakraRestoreMessage.Handler.class,
            ProcedureChakraRestore.ChakraRestoreMessage.class,
            0, // 消息ID，确保唯一
            Side.SERVER
        );
    }
}
