package net.narutomod.world.biome;

import net.narutomod.ElementsNarutomodMod;

import net.minecraft.world.biome.Biome;
import net.minecraft.world.biome.BiomeDecorator;
import net.minecraft.world.gen.feature.WorldGenAbstractTree;
import net.minecraft.world.World;
import net.minecraft.util.math.BlockPos;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.entity.EnumCreatureType;
import net.minecraft.init.Blocks;

import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.common.registry.ForgeRegistries;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;

import java.util.Random;

@ElementsNarutomodMod.ModElement.Tag
public class BiomeKotoamatsukami extends ElementsNarutomodMod.ModElement {
	
	public static Biome biome;
	
	public BiomeKotoamatsukami(ElementsNarutomodMod instance) {
		super(instance, 606);
	}

	@Override
	public void initElements() {
		elements.biomes.add(() -> biome);
	}

	@Override
	public void init(FMLInitializationEvent event) {
		// 生物群系在静态初始化块中已经注册
	}

	static {
		Biome.BiomeProperties properties = new Biome.BiomeProperties("Kotoamatsukami");
		properties.setRainfall(0.0F);
		properties.setBaseHeight(0.1F);
		properties.setHeightVariation(0.0F);
		properties.setTemperature(0.8F);
		properties.setRainDisabled();
		properties.setWaterColor(0x8B0000); // 深红色水

		biome = new CustomBiome(properties);
		biome.setRegistryName("kotoamatsukami");
		ForgeRegistries.BIOMES.register(biome);
	}

	static class CustomBiome extends Biome {
		
		public CustomBiome(Biome.BiomeProperties properties) {
			super(properties);
			
			// 设置顶层和填充方块
			this.topBlock = Blocks.BEDROCK.getDefaultState();
			this.fillerBlock = Blocks.BEDROCK.getDefaultState();
			
			// 清除所有生物生成
			this.spawnableCreatureList.clear();
			this.spawnableMonsterList.clear();
			this.spawnableWaterCreatureList.clear();
			this.spawnableCaveCreatureList.clear();
			
			// 设置装饰器
			this.decorator = new CustomBiomeDecorator();
		}

		@Override
		@SideOnly(Side.CLIENT)
		public int getGrassColorAtPos(BlockPos pos) {
			return 0x8B0000; // 深红色草地
		}

		@Override
		@SideOnly(Side.CLIENT)
		public int getFoliageColorAtPos(BlockPos pos) {
			return 0x8B0000; // 深红色叶子
		}

		@Override
		@SideOnly(Side.CLIENT)
		public int getSkyColorByTemp(float currentTemperature) {
			return 0x8B0000; // 深红色天空
		}

		@Override
		public WorldGenAbstractTree getRandomTreeFeature(Random rand) {
			return null; // 不生成树木
		}

		@Override
		public boolean canRain() {
			return false; // 不下雨
		}

		@Override
		public boolean getEnableSnow() {
			return false; // 不下雪
		}
	}

	static class CustomBiomeDecorator extends BiomeDecorator {
		
		@Override
		protected void genDecorations(Biome biomeIn, World worldIn, Random random) {
			// 不生成任何装饰物
			// 保持空旷的别天神世界
		}
	}
}
