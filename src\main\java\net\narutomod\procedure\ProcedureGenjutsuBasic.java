package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.Chakra;
import net.narutomod.PlayerTracker;

import net.minecraft.world.World;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.ResourceLocation;
import net.minecraft.potion.PotionEffect;
import net.minecraft.init.MobEffects;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.item.ItemStack;

import java.util.Map;
import java.util.HashMap;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureGenjutsuBasic extends ElementsNarutomodMod.ModElement {
	
	// 基础幻术技能配置
	private static final double CHAKRA_COST = 800.0D;
	private static final double MIN_CHAKRA_REQUIRED = 4000.0D;
	private static final int DURATION = 200; // 10秒 (缩短时间)
	private static final int COOLDOWN = 1200; // 1分钟冷却
	private static final double MIN_BATTLE_XP = 1000.0D;
	private static final String COOLDOWN_KEY = "genjutsu_basic_cd";
	
	// 活跃幻术追踪
	private static final Map<Integer, EntityLivingBase> genjutsuList = new HashMap<>();
	
	public ProcedureGenjutsuBasic(ElementsNarutomodMod instance) {
		super(instance, 607);
	}

	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure ProcedureGenjutsuBasic!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure ProcedureGenjutsuBasic!");
			return;
		}
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure ProcedureGenjutsuBasic!");
			return;
		}
		
		Entity entity = (Entity) dependencies.get("entity");
		boolean is_pressed = (Boolean) dependencies.get("is_pressed");
		
		if (!(entity instanceof EntityLivingBase)) {
			System.err.println("Unauthorized calling of procedure ProcedureGenjutsuBasic!");
			return;
		}
		
		// 只在按下时执行，松开时不执行任何操作
		if (!is_pressed) {
			return;
		}
		
		if (entity instanceof EntityPlayer && 
			Chakra.pathway((EntityLivingBase) entity).getMax() < MIN_CHAKRA_REQUIRED) {
			((EntityPlayer) entity).sendMessage(new TextComponentString(TextFormatting.RED + 
				"需要至少 " + (int)MIN_CHAKRA_REQUIRED + " 查克拉上限才能使用基础幻术！"));
			return;
		}
		
		execute((EntityLivingBase) entity);
	}

	// 基于源项目的execute方法
	public static void execute(EntityLivingBase entity) {
		if (entity == null || entity.world == null) {
			return;
		}

		World world = entity.world;

		// 检查冷却时间
		if (entity instanceof EntityPlayer) {
			EntityPlayer player = (EntityPlayer) entity;
			double cooldown = player.getEntityData().getDouble(COOLDOWN_KEY);
			if (world.getTotalWorldTime() < cooldown) {
				long remainingTime = (long)((cooldown - world.getTotalWorldTime()) / 20);
				player.sendStatusMessage(new net.minecraft.util.text.TextComponentString(
					net.minecraft.util.text.TextFormatting.RED + "基础幻术冷却中: " + remainingTime + "秒"), true);
				return;
			}
		}

		// 激活基础幻术
		if (entity instanceof EntityPlayer) {
			ItemStack helmet = ((EntityPlayer) entity).inventory.armorInventory.get(3);
			if (!helmet.isEmpty() && helmet.hasTagCompound()) {
				net.minecraft.nbt.NBTTagCompound tag = helmet.getTagCompound();
				if (tag != null && tag.getBoolean("sharingan_blinded")) {
					return;
				}
			}
			
			if (PlayerTracker.getBattleXp((EntityPlayer) entity) < MIN_BATTLE_XP) {
				return;
			}
		}

		// 寻找目标
		RayTraceResult trace = ProcedureUtils.objectEntityLookingAt(entity, 20.0D);
		EntityLivingBase target = null;

		if (trace.typeOfHit == RayTraceResult.Type.ENTITY && trace.entityHit instanceof EntityLivingBase) {
			target = (EntityLivingBase) trace.entityHit;
		}

		if (target != null && Chakra.pathway(entity).consume(CHAKRA_COST)) {
			// 激活基础幻术效果
			activateGenjutsu(target);

			if (entity instanceof EntityPlayer) {
				((EntityPlayer) entity).sendMessage(new TextComponentString(TextFormatting.DARK_PURPLE + "基础幻术发动！"));
				// 设置冷却时间
				((EntityPlayer) entity).getEntityData().setDouble(COOLDOWN_KEY, world.getTotalWorldTime() + COOLDOWN);
			}

			genjutsuList.put(entity.getEntityId(), target);

			// 播放音效
			net.minecraft.util.SoundEvent soundEvent = net.minecraft.util.SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:genjutsu"));
			if (soundEvent != null) {
				world.playSound(null, entity.posX, entity.posY, entity.posZ, soundEvent, SoundCategory.NEUTRAL, 1.0F, 1.0F);
			}
		}
	}

	// 激活基础幻术效果
	private static void activateGenjutsu(EntityLivingBase target) {
		// 对目标施加基础幻术效果
		target.addPotionEffect(new PotionEffect(net.narutomod.potion.PotionParalysis.potion, DURATION, 0, false, false));
		target.addPotionEffect(new PotionEffect(MobEffects.BLINDNESS, DURATION, 0, false, false));
		target.addPotionEffect(new PotionEffect(MobEffects.NAUSEA, DURATION, 0, false, true));
		
		// 给目标发送消息
		if (target instanceof EntityPlayer) {
			((EntityPlayer) target).sendMessage(new TextComponentString(TextFormatting.GOLD + "你被基础幻术困住了！"));
		}
		
		// 设置幻术数据
		target.getEntityData().setBoolean("genjutsu_basic_active", true);
		target.getEntityData().setInteger("genjutsu_basic_time", DURATION);
	}

	// 清除基础幻术效果
	public static void clearGenjutsu(EntityLivingBase target) {
		if (target.getEntityData().hasKey("genjutsu_basic_time")) {
			target.getEntityData().removeTag("genjutsu_basic_time");
			target.getEntityData().removeTag("genjutsu_basic_active");
			
			// 移除相关药水效果
			target.removePotionEffect(net.narutomod.potion.PotionParalysis.potion);
			target.removePotionEffect(MobEffects.BLINDNESS);
			target.removePotionEffect(MobEffects.NAUSEA);
			
			if (target instanceof EntityPlayer) {
				((EntityPlayer) target).sendMessage(new TextComponentString(TextFormatting.GREEN + "基础幻术效果已解除！"));
			}
		}
	}

	// 检查并清理过期的基础幻术效果
	public static void checkGenjutsuExpiry(EntityLivingBase entity, World world) {
		if (entity.getEntityData().hasKey("genjutsu_basic_time")) {
			int time = entity.getEntityData().getInteger("genjutsu_basic_time");
			if (time > 0) {
				entity.getEntityData().setInteger("genjutsu_basic_time", time - 1);
			} else {
				clearGenjutsu(entity);
			}
		}
	}

	// 清除施术者的所有基础幻术效果
	public static void clearCasterGenjutsu(EntityLivingBase caster) {
		EntityLivingBase target = genjutsuList.get(caster.getEntityId());
		if (target != null) {
			clearGenjutsu(target);
			genjutsuList.remove(caster.getEntityId());
		}
	}

	// 获取施术者的基础幻术目标（用于Shift+忍术3解除功能）
	public static EntityLivingBase getCasterTarget(EntityLivingBase caster) {
		return genjutsuList.get(caster.getEntityId());
	}

	// 移除施术者的基础幻术追踪（用于Shift+忍术3解除功能）
	public static void removeCasterTarget(EntityLivingBase caster) {
		genjutsuList.remove(caster.getEntityId());
	}
}
