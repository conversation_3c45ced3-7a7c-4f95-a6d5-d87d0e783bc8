package net.narutomod.shuxing;

import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.command.WrongUsageException;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.math.BlockPos;
import java.util.Collections;
import java.util.List;

public class CommandGiveAttributePoints extends CommandBase {
    
    @Override
    public String getName() {
        return "giveattributepoints";
    }
    
    @Override
    public String getUsage(ICommandSender sender) {
        return "/giveattributepoints <玩家> <点数> - 给予或扣除玩家属性点（正数增加，负数扣除，0查看）\n" +
               "/giveattributepoints debug <玩家> - 显示玩家详细属性信息";
    }

    @Override
    public void execute(MinecraftServer server, ICommandSender sender, String[] args) throws CommandException {
        if (args.length < 1 || args.length > 2) {
            throw new WrongUsageException(getUsage(sender));
        }

        // 调试命令
        if (args[0].equals("debug") && args.length == 2) {
            EntityPlayerMP targetPlayer = CommandBase.getPlayer(server, sender, args[1]);
            AttributeEnhancement attr = AttributeEnhancement.get(targetPlayer);

            if (attr != null) {
                sender.sendMessage(new TextComponentString("§e=== 玩家 " + targetPlayer.getName() + " 属性信息 ==="));
                sender.sendMessage(new TextComponentString("§a可用点数: " + attr.getAvailablePoints()));
                sender.sendMessage(new TextComponentString("§b总分配点数: " + attr.getTotalAllocatedPoints()));

                for (int i = 0; i < AttributeManager.ATTRIBUTE_NAMES.length; i++) {
                    sender.sendMessage(new TextComponentString("§f" +
                        AttributeManager.ATTRIBUTE_NAMES[i] + ": " + attr.getAttributeLevel(i)));
                }

                // 强制同步
                attr.syncToClient(targetPlayer);
                sender.sendMessage(new TextComponentString("§a已强制同步属性数据"));
            } else {
                sender.sendMessage(new TextComponentString("§c玩家属性数据为空"));
            }
            return;
        }

        if (args.length != 2) {
            throw new WrongUsageException(getUsage(sender));
        }
        
        EntityPlayerMP targetPlayer = CommandBase.getPlayer(server, sender, args[0]);
        int points = CommandBase.parseInt(args[1]); // 移除最小值限制，允许负数
        
        AttributeEnhancement attr = AttributeEnhancement.get(targetPlayer);
        if (attr != null) {
            if (points > 0) {
                // 正数：增加属性点
                attr.addAvailablePoints(points);
                sender.sendMessage(new TextComponentString(
                    String.format("§a成功给予 %s %d 个属性点", targetPlayer.getName(), points)
                ));
                targetPlayer.sendMessage(new TextComponentString(
                    String.format("§a你获得了 %d 个属性点！", points)
                ));
            } else if (points < 0) {
                // 负数：扣除属性点
                int currentPoints = attr.getAvailablePoints();
                int deductPoints = Math.abs(points);
                
                if (currentPoints >= deductPoints) {
                    attr.addAvailablePoints(points); // points已经是负数
                    sender.sendMessage(new TextComponentString(
                        String.format("§c成功扣除 %s %d 个属性点", targetPlayer.getName(), deductPoints)
                    ));
                    targetPlayer.sendMessage(new TextComponentString(
                        String.format("§c你失去了 %d 个属性点！", deductPoints)
                    ));
                } else {
                    sender.sendMessage(new TextComponentString(
                        String.format("§c%s 只有 %d 个属性点，无法扣除 %d 个", 
                            targetPlayer.getName(), currentPoints, deductPoints)
                    ));
                    return;
                }
            } else {
                // 0：显示当前属性点
                sender.sendMessage(new TextComponentString(
                    String.format("§e%s 当前有 %d 个属性点", targetPlayer.getName(), attr.getAvailablePoints())
                ));
                return;
            }
            
            // 同步到客户端
            attr.syncToClient(targetPlayer);
        }
    }
    
    @Override
    public int getRequiredPermissionLevel() {
        return 2;
    }

    @Override
    public List<String> getTabCompletions(MinecraftServer server, ICommandSender sender, String[] args, BlockPos targetPos) {
        if (args.length == 1) {
            // 第一个参数：玩家名补全
            return getListOfStringsMatchingLastWord(args, server.getOnlinePlayerNames());
        } else if (args.length == 2) {
            // 第二个参数：常用数值补全
            return getListOfStringsMatchingLastWord(args, "1", "5", "10", "20", "-1", "-5", "-10");
        }
        return Collections.emptyList();
    }
}
