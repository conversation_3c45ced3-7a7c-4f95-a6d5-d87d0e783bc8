package net.narutomod.shuxing;

import io.netty.buffer.ByteBuf;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

public class PacketRequestSync implements IMessage {
    
    public PacketRequestSync() {}
    
    @Override
    public void fromBytes(ByteBuf buf) {
        // 空包，不需要数据
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        // 空包，不需要数据
    }
    
    public static class Handler implements IMessageHandler<PacketRequestSync, IMessage> {
        @Override
        public IMessage onMessage(PacketRequestSync message, MessageContext ctx) {
            EntityPlayerMP player = ctx.getServerHandler().player;
            player.getServerWorld().addScheduledTask(() -> {
                AttributeEnhancement attr = AttributeEnhancement.get(player);
                if (attr != null) {
                    // 强制同步到客户端
                    attr.syncToClient(player);
                } else {
                    // 如果属性为空，创建新的属性实例
                    System.out.println("为玩家 " + player.getName() + " 创建新的属性实例");
                    AttributeManager.forceRefreshPlayer(player);
                }
            });
            return null;
        }
    }
}
