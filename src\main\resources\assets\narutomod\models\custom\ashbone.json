{"credit": "Made with Blockbench", "ambientocclusion": false, "elements": [{"from": [7, 7, 4], "to": [9, 9, 16], "rotation": {"angle": 0, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [4, 2, 2, 0], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 90, "texture": "#0"}, "south": {"uv": [6, 0, 4, 2], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 270, "texture": "#0"}, "up": {"uv": [6, 2, 8, 16], "texture": "#0"}, "down": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}}}, {"from": [7.2, 7.2, -1.8], "to": [8.8, 8.8, 9.8], "rotation": {"angle": 0, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [4, 2, 2, 0], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 90, "texture": "#0"}, "south": {"uv": [6, 0, 4, 2], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 270, "texture": "#0"}, "up": {"uv": [6, 2, 8, 16], "texture": "#0"}, "down": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}}}, {"from": [7.4, 7.4, -4.6], "to": [8.6, 8.6, 6.6], "rotation": {"angle": 0, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [4, 2, 2, 0], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 90, "texture": "#0"}, "south": {"uv": [6, 0, 4, 2], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 270, "texture": "#0"}, "up": {"uv": [6, 2, 8, 16], "texture": "#0"}, "down": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}}}, {"from": [7.7, 7.7, -6.3], "to": [8.3, 8.3, 4.3], "rotation": {"angle": 0, "axis": "x", "origin": [8, 8, 8]}, "faces": {"north": {"uv": [4, 2, 2, 0], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 90, "texture": "#0"}, "south": {"uv": [6, 0, 4, 2], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 270, "texture": "#0"}, "up": {"uv": [6, 2, 8, 16], "texture": "#0"}, "down": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}}}], "display": {"thirdperson_righthand": {"rotation": [0, 0, -45], "translation": [0, -2, -3], "scale": [0.8, 0.8, 0.8]}, "thirdperson_lefthand": {"rotation": [0, 0, 45], "translation": [0, -2, -3], "scale": [0.8, 0.8, 0.8]}, "firstperson_righthand": {"rotation": [0, 0, -45], "translation": [0, -2, -3], "scale": [0.8, 0.8, 0.8]}, "firstperson_lefthand": {"rotation": [0, 0, 45], "translation": [0, -2, -3], "scale": [0.8, 0.8, 0.8]}, "ground": {"rotation": [0, -90, -45], "translation": [-8, 0, 0]}, "gui": {"rotation": [90, -45, 30], "translation": [-1.5, -2, -1.5], "scale": [0.9, 0.9, 0.9]}, "fixed": {"rotation": [90, 45, 30], "translation": [1.5, -1.5, -1.5]}}, "groups": [{"name": "group", "origin": [0, 0, 0], "color": 0, "children": [0, 1, 2, 3]}]}