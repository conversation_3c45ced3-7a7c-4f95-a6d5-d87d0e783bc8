package net.narutomod.yongheng;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import net.narutomod.Chakra;
import net.narutomod.ModConfig;
import net.narutomod.Particles;
import io.netty.buffer.ByteBuf;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;

public class ProcedureChakraRestore {
    private static final double RESTORE_RATE = 2.0; // 每tick恢复的查克拉量
    private static final String RESTORING_KEY = "chakra_restoring";
    private static final String LAST_POS_X = "last_pos_x";
    private static final String LAST_POS_Y = "last_pos_y";
    private static final String LAST_POS_Z = "last_pos_z";
    private static final String START_TIME_KEY = "chakra_start_time";
    private static final int START_DELAY = 20; // 1秒延迟(20 ticks)
    private static final int MAX_RESTORE_TIME = 6000; // 5分钟超时(6000 ticks)
    
    @SubscribeEvent
    public void onPlayerTick(TickEvent.PlayerTickEvent event) {
        if (event.phase == TickEvent.Phase.END && !event.player.world.isRemote) {
            EntityPlayer player = event.player;
            
            if (player.getEntityData().getBoolean(RESTORING_KEY)) {
                // 检查延迟时间
                long startTime = player.getEntityData().getLong(START_TIME_KEY);
                long currentTime = player.world.getTotalWorldTime();
                long elapsed = currentTime - startTime;

                // 超时检查
                if (elapsed > MAX_RESTORE_TIME) {
                    System.out.println("玩家 " + player.getName() + " 查克拉恢复超时，自动停止");
                    stopRestore(player);
                    return;
                }

                if (elapsed < START_DELAY) {
                    // 还在延迟期间，显示准备状态
                    if (elapsed % 10 == 0) {
                        player.sendStatusMessage(new TextComponentString("§e准备聚集查克拉... (" + elapsed + "/" + START_DELAY + ")"), true);
                        System.out.println("玩家 " + player.getName() + " 准备阶段: " + elapsed + "/" + START_DELAY);
                    }

                    // 播放准备粒子效果
                    spawnPreparationParticles(player);
                    updatePlayerPosition(player);
                    return;
                } else if (elapsed == START_DELAY) {
                    // 只在刚完成准备阶段时输出一次
                    System.out.println("玩家 " + player.getName() + " 准备阶段完成，开始恢复查克拉");
                }
                
                // 检查是否在移动
                boolean isMoving = isPlayerMoving(player);
                
                // 恢复查克拉
                Chakra.Pathway pathway = Chakra.pathway(player);
                if (pathway != null) {
                    double current = pathway.getAmount();
                    double max = pathway.getMax();
                    
                    if (current < max) {
                        // 应用体质强化加成
                        double restoreAmount = RESTORE_RATE;
                        
                        // 移动时效率减半
                        if (isMoving) {
                            restoreAmount *= 0.5;
                        }
                        
                        restoreAmount = net.narutomod.shuxing.AttributeManager.applyChakraRegenBonus(player, (float)restoreAmount);
                        
                        pathway.consume(-restoreAmount, true); // 使用consume方法，负数表示增加
                        
                        // 显示恢复进度 - 每2秒更新一次
                        if (player.ticksExisted % 40 == 0) {
                            double percentage = (current / max) * 100;
                            String statusText = isMoving ?
                                String.format("§6移动中聚集查克拉... %.1f%% (效率减半)", percentage) :
                                String.format("§b聚集查克拉中... %.1f%%", percentage);
                            player.sendStatusMessage(new TextComponentString(statusText), true);
                        }
                        
                        // 播放粒子效果 - 移动时粒子效果也会减弱
                        if (isMoving) {
                            spawnMovingChakraParticles(player);
                        } else {
                            spawnChakraParticles(player);
                        }
                        
                        // 播放音效
                        if (player.ticksExisted % 30 == 0) {
                            playChakraSound(player);
                        }
                    } else {
                        // 查克拉已满
                        player.getEntityData().setBoolean(RESTORING_KEY, false);
                        player.sendStatusMessage(new TextComponentString("§a查克拉已恢复至满值！"), true);
                        // 播放完成音效
                        player.world.playSound(null, player.posX, player.posY, player.posZ,
                            SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:dojutsu")),
                            SoundCategory.PLAYERS, 0.8F, 1.2F);
                    }
                }
                
                // 更新位置记录
                updatePlayerPosition(player);
            }
        }
    }
    
    private void spawnPreparationParticles(EntityPlayer player) {
        // 准备阶段的淡蓝色粒子 - 增强效果
        for (int i = 0; i < 5; i++) {
            double angle = player.getRNG().nextDouble() * Math.PI * 2;
            double radius = 0.8 + player.getRNG().nextDouble() * 0.5;
            
            double startX = player.posX + Math.cos(angle) * radius;
            double startY = player.posY + 0.5 + player.getRNG().nextDouble() * 0.8;
            double startZ = player.posZ + Math.sin(angle) * radius;
            
            // 轻微的向心运动
            double motionX = (player.posX - startX) * 0.02;
            double motionY = 0.03d;
            double motionZ = (player.posZ - startZ) * 0.02;
            
            Particles.spawnParticle(player.world, Particles.Types.SMOKE, 
                startX, startY, startZ, 1,
                0d, 0d, 0d, motionX, motionY, motionZ, 
                0x5080CCFF, 35, 1, 0xF0, player.getEntityId()); // 准备阶段蓝色
        }
    }
    
    private void spawnChakraParticles(EntityPlayer player) {
        // 向中心聚集的蓝色查克拉粒子 - 增加数量和范围
        for (int i = 0; i < 12; i++) {
            double angle = player.getRNG().nextDouble() * Math.PI * 2;
            double radius = 1.2 + player.getRNG().nextDouble() * 1.0; // 扩大范围
            double height = player.getRNG().nextDouble() * 1.5;
            
            double startX = player.posX + Math.cos(angle) * radius;
            double startY = player.posY + height;
            double startZ = player.posZ + Math.sin(angle) * radius;
            
            // 计算向玩家中心的运动向量 - 增强吸收效果
            double motionX = (player.posX - startX) * 0.12;
            double motionY = (player.posY + 1.0 - startY) * 0.12;
            double motionZ = (player.posZ - startZ) * 0.12;
            
            Particles.spawnParticle(player.world, Particles.Types.SMOKE, 
                startX, startY, startZ, 1,
                0d, 0d, 0d, motionX, motionY, motionZ, 
                0x4080FFFF, 50, 3, 0xF0, player.getEntityId()); // 更蓝的颜色
        }
        
        // 额外的远距离粒子 - 从更远处吸收
        for (int i = 0; i < 6; i++) {
            double angle = player.getRNG().nextDouble() * Math.PI * 2;
            double radius = 2.0 + player.getRNG().nextDouble() * 1.5; // 更远的距离
            double height = player.getRNG().nextDouble() * 2.0;
            
            double startX = player.posX + Math.cos(angle) * radius;
            double startY = player.posY + height;
            double startZ = player.posZ + Math.sin(angle) * radius;
            
            // 更强的向心运动
            double motionX = (player.posX - startX) * 0.08;
            double motionY = (player.posY + 1.0 - startY) * 0.08;
            double motionZ = (player.posZ - startZ) * 0.08;
            
            Particles.spawnParticle(player.world, Particles.Types.SMOKE, 
                startX, startY, startZ, 1,
                0d, 0d, 0d, motionX, motionY, motionZ, 
                0x2060DDFF, 60, 2, 0xF0, player.getEntityId()); // 深蓝色
        }
        
        // 螺旋聚集效果
        if (player.ticksExisted % 3 == 0) {
            double spiralAngle = (player.ticksExisted * 0.3) % (Math.PI * 2);
            for (int i = 0; i < 3; i++) {
                double currentAngle = spiralAngle + (i * Math.PI * 2 / 3);
                double spiralRadius = 1.5 - (player.ticksExisted % 30) * 0.05; // 螺旋收缩
                
                double spiralX = player.posX + Math.cos(currentAngle) * spiralRadius;
                double spiralZ = player.posZ + Math.sin(currentAngle) * spiralRadius;
                double spiralY = player.posY + 0.5 + Math.sin(player.ticksExisted * 0.1) * 0.3;
                
                // 向中心的螺旋运动
                double motionX = (player.posX - spiralX) * 0.15;
                double motionY = (player.posY + 1.0 - spiralY) * 0.1;
                double motionZ = (player.posZ - spiralZ) * 0.15;
                
                Particles.spawnParticle(player.world, Particles.Types.SMOKE,
                    spiralX, spiralY, spiralZ, 1,
                    0d, 0d, 0d, motionX, motionY, motionZ,
                    0x6090FFFF, 40, 2, 0xF0, player.getEntityId()); // 亮蓝色螺旋
            }
        }
        
        // 中心聚集的能量核心效果 - 更频繁更亮
        if (player.ticksExisted % 15 == 0) {
            Particles.spawnParticle(player.world, Particles.Types.EXPANDING_SPHERE, 
                player.posX, player.posY + 1.0, player.posZ, 1,
                0d, 0d, 0d, 0d, 0d, 0d, 0x80C0FFFF, 20, 0); // 非常亮的蓝色核心
        }
        
        // 地面能量波纹效果
        if (player.ticksExisted % 25 == 0) {
            Particles.spawnParticle(player.world, Particles.Types.EXPANDING_SPHERE, 
                player.posX, player.posY + 0.1, player.posZ, 1,
                0d, 0d, 0d, 0d, 0d, 0d, 0x4070DDFF, 30, 0); // 地面波纹
        }
    }
    
    private void playChakraSound(EntityPlayer player) {
        // 只使用蓄力音效，去掉duangduang的声音
        float pitch = player.getRNG().nextFloat() * 0.2F + 1.0F;
        
        // 只使用充能音效
        player.world.playSound(null, player.posX, player.posY, player.posZ,
            SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:charging_chakra")),
            SoundCategory.PLAYERS, 0.15F, pitch);
    }
    
    private boolean isPlayerMoving(EntityPlayer player) {
        double lastX = player.getEntityData().getDouble(LAST_POS_X);
        double lastY = player.getEntityData().getDouble(LAST_POS_Y);
        double lastZ = player.getEntityData().getDouble(LAST_POS_Z);
        
        double threshold = 0.005; // 更严格的移动阈值
        return Math.abs(player.posX - lastX) > threshold || 
               Math.abs(player.posY - lastY) > threshold || 
               Math.abs(player.posZ - lastZ) > threshold;
    }
    
    private static void updatePlayerPosition(EntityPlayer player) {
        player.getEntityData().setDouble(LAST_POS_X, player.posX);
        player.getEntityData().setDouble(LAST_POS_Y, player.posY);
        player.getEntityData().setDouble(LAST_POS_Z, player.posZ);
    }
    
    public static void startRestore(EntityPlayer player) {
        if (!player.world.isRemote) {
            player.getEntityData().setBoolean(RESTORING_KEY, true);
            player.getEntityData().setLong(START_TIME_KEY, player.world.getTotalWorldTime());
            updatePlayerPosition(player);
            player.sendStatusMessage(new TextComponentString("§e准备聚集查克拉..."), true);

            // 播放开始音效
            player.world.playSound(null, player.posX, player.posY, player.posZ,
                SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:charging_chakra")),
                SoundCategory.PLAYERS, 0.2F, 1.1F);
        }
    }
    
    public static void stopRestore(EntityPlayer player) {
        if (!player.world.isRemote) {
            boolean wasRestoring = player.getEntityData().getBoolean(RESTORING_KEY);
            player.getEntityData().setBoolean(RESTORING_KEY, false);

            // 清除相关数据
            player.getEntityData().removeTag(START_TIME_KEY);
            player.getEntityData().removeTag(LAST_POS_X);
            player.getEntityData().removeTag(LAST_POS_Y);
            player.getEntityData().removeTag(LAST_POS_Z);

            if (wasRestoring) {
                player.sendStatusMessage(new TextComponentString("§7停止聚集查克拉"), true);

                // 播放停止音效
                player.world.playSound(null, player.posX, player.posY, player.posZ,
                    SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:chakraflow")),
                    SoundCategory.PLAYERS, 0.1F, 0.8F);
            }
        }
    }
    
    // 网络消息处理
    public static void sendKeyPress(boolean pressed) {
        try {
            net.narutomod.yongheng.PacketHandler.INSTANCE.sendToServer(new ChakraRestoreMessage(pressed));
        } catch (Exception e) {
            System.out.println("发送查克拉恢复消息失败: " + e.getMessage());
        }
    }
    
    public static class ChakraRestoreMessage implements IMessage {
        private boolean pressed;
        
        public ChakraRestoreMessage() {}
        
        public ChakraRestoreMessage(boolean pressed) {
            this.pressed = pressed;
        }
        
        @Override
        public void fromBytes(ByteBuf buf) {
            this.pressed = buf.readBoolean();
        }
        
        @Override
        public void toBytes(ByteBuf buf) {
            buf.writeBoolean(this.pressed);
        }
        
        public static class Handler implements IMessageHandler<ChakraRestoreMessage, IMessage> {
            @Override
            public IMessage onMessage(ChakraRestoreMessage message, MessageContext ctx) {
                EntityPlayerMP player = ctx.getServerHandler().player;
                player.getServerWorld().addScheduledTask(() -> {
                    // 简化日志输出
                    System.out.println("玩家 " + player.getName() + " " + (message.pressed ? "开始" : "停止") + "查克拉恢复");
                    if (message.pressed) {
                        startRestore(player);
                    } else {
                        stopRestore(player);
                    }
                });
                return null;
            }
        }
    }
    
    private void spawnMovingChakraParticles(EntityPlayer player) {
        // 移动时的减弱粒子效果
        for (int i = 0; i < 6; i++) { // 减少粒子数量
            double angle = player.getRNG().nextDouble() * Math.PI * 2;
            double radius = 0.6 + player.getRNG().nextDouble() * 0.8; // 缩小范围
            double height = player.getRNG().nextDouble() * 1.0;
            
            double startX = player.posX + Math.cos(angle) * radius;
            double startY = player.posY + height;
            double startZ = player.posZ + Math.sin(angle) * radius;
            
            // 较弱的向心运动
            double motionX = (player.posX - startX) * 0.06;
            double motionY = (player.posY + 0.8 - startY) * 0.06;
            double motionZ = (player.posZ - startZ) * 0.06;
            
            Particles.spawnParticle(player.world, Particles.Types.SMOKE, 
                startX, startY, startZ, 1,
                0d, 0d, 0d, motionX, motionY, motionZ, 
                0x3070DDFF, 35, 2, 0xF0, player.getEntityId()); // 较暗的蓝色
        }
        
        // 简化的中心效果
        if (player.ticksExisted % 30 == 0) {
            Particles.spawnParticle(player.world, Particles.Types.EXPANDING_SPHERE, 
                player.posX, player.posY + 0.8, player.posZ, 1,
                0d, 0d, 0d, 0d, 0d, 0d, 0x6090DDFF, 12, 0); // 较小的核心
        }
    }
}



