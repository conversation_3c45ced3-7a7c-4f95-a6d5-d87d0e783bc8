package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.Chakra;
import net.narutomod.PlayerTracker;
import net.narutomod.NarutomodModVariables;
import net.narutomod.potion.PotionParalysis;
import net.narutomod.item.ItemSharingan;

import net.minecraft.world.World;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.ResourceLocation;
import net.minecraft.potion.PotionEffect;
import net.minecraft.init.MobEffects;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.entity.monster.EntityMob;
import net.minecraft.item.ItemStack;
import net.minecraft.inventory.EntityEquipmentSlot;

import java.util.Map;
import java.util.HashMap;
import java.util.UUID;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureGenjutsu extends ElementsNarutomodMod.ModElement {
	
	// 幻术状态追踪
	public static Map<UUID, Integer> genjutsuList = new HashMap<>();
	
	// 幻术技能配置
	private static final double CHAKRA_COST = 800.0D;
	private static final int DURATION = 400; // 20秒
	private static final int COOLDOWN = 1200; // 1分钟冷却
	
	public ProcedureGenjutsu(ElementsNarutomodMod instance) {
		super(instance, 603);
	}

	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure ProcedureGenjutsu!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure ProcedureGenjutsu!");
			return;
		}
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure ProcedureGenjutsu!");
			return;
		}

		Entity entity = (Entity) dependencies.get("entity");
		World world = (World) dependencies.get("world");
		boolean is_pressed = (Boolean) dependencies.get("is_pressed");

		if (!(entity instanceof EntityLivingBase)) {
			return;
		}

		EntityLivingBase caster = (EntityLivingBase) entity;

		// 只在按下时执行
		if (!is_pressed) {
			return;
		}

		// 检查是否为玩家且满足条件
		if (entity instanceof EntityPlayer) {
			EntityPlayer player = (EntityPlayer) entity;
			
			// 检查头盔
			ItemStack helmet = player.inventory.armorInventory.get(3);
			if (helmet.hasTagCompound() && helmet.getTagCompound().getBoolean("sharingan_blinded")) {
				player.sendMessage(new TextComponentString(TextFormatting.RED + "写轮眼已失明，无法使用幻术！"));
				return;
			}

			// 检查冷却时间
			double cooldown = player.getEntityData().getDouble("genjutsu_cd");
			if (world.getTotalWorldTime() < cooldown) {
				long remainingTime = (long)((cooldown - world.getTotalWorldTime()) / 20);
				player.sendMessage(new TextComponentString(TextFormatting.RED + "幻术冷却中，剩余时间: " + remainingTime + " 秒"));
				return;
			}

			// 检查查克拉
			if (!Chakra.pathway(player).consume(CHAKRA_COST)) {
				player.sendMessage(new TextComponentString(TextFormatting.RED + "查克拉不足！需要 " + (int)CHAKRA_COST + " 查克拉"));
				return;
			}
		}

		// 寻找目标
		RayTraceResult trace = ProcedureUtils.objectEntityLookingAt(entity, 10.0D);
		EntityLivingBase target = null;

		if (trace.typeOfHit == RayTraceResult.Type.ENTITY && trace.entityHit instanceof EntityLivingBase) {
			target = (EntityLivingBase) trace.entityHit;
		}

		if (target == null) {
			if (entity instanceof EntityPlayer) {
				((EntityPlayer) entity).sendMessage(new TextComponentString(TextFormatting.RED + "未找到有效目标！"));
			}
			return;
		}

		// 执行幻术
		executeGenjutsu(caster, target, world);
	}

	private static void executeGenjutsu(EntityLivingBase caster, EntityLivingBase target, World world) {
		// 设置冷却时间
		if (caster instanceof EntityPlayer) {
			caster.getEntityData().setDouble("genjutsu_cd", world.getTotalWorldTime() + COOLDOWN);
			((EntityPlayer) caster).sendMessage(new TextComponentString(TextFormatting.DARK_PURPLE + "幻术发动！"));
		}

		// 记录幻术关系
		genjutsuList.put(target.getUniqueID(), caster.getEntityId());

		// 对目标施加幻术效果
		target.addPotionEffect(new PotionEffect(PotionParalysis.potion, DURATION, 1, false, false));
		target.addPotionEffect(new PotionEffect(MobEffects.BLINDNESS, DURATION + 40, 0, false, true));
		target.addPotionEffect(new PotionEffect(MobEffects.NAUSEA, DURATION, 0, false, true));

		// 播放音效
		world.playSound(null, caster.posX, caster.posY, caster.posZ, 
			net.minecraft.util.SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:genjutsu")), 
			SoundCategory.NEUTRAL, 1.0F, 1.0F);

		// 给目标发送消息
		if (target instanceof EntityPlayer) {
			((EntityPlayer) target).sendMessage(new TextComponentString(TextFormatting.DARK_PURPLE + "你被幻术困住了！"));
		}

		// 设置目标数据
		target.getEntityData().setBoolean("genjutsu_active", true);
		target.getEntityData().setInteger("genjutsu_caster", caster.getEntityId());
		target.getEntityData().setLong("genjutsu_end_time", world.getTotalWorldTime() + DURATION);

		// 如果目标是怪物，让它停止攻击
		if (target instanceof EntityMob) {
			EntityMob mob = (EntityMob) target;
			mob.setAttackTarget(null);
			mob.setRevengeTarget(null);
		}
	}

	// 清除幻术效果
	public static void clearGenjutsu(EntityLivingBase target) {
		if (target.getEntityData().getBoolean("genjutsu_active")) {
			target.getEntityData().setBoolean("genjutsu_active", false);
			target.getEntityData().removeTag("genjutsu_caster");
			target.getEntityData().removeTag("genjutsu_end_time");
			
			// 移除相关药水效果
			target.removePotionEffect(PotionParalysis.potion);
			target.removePotionEffect(MobEffects.BLINDNESS);
			target.removePotionEffect(MobEffects.NAUSEA);
			
			if (target instanceof EntityPlayer) {
				((EntityPlayer) target).sendMessage(new TextComponentString(TextFormatting.GREEN + "幻术效果已解除！"));
			}
			
			// 从追踪列表中移除
			genjutsuList.remove(target.getUniqueID());
		}
	}

	// 检查并清理过期的幻术效果
	public static void checkGenjutsuExpiry(EntityLivingBase entity, World world) {
		if (entity.getEntityData().getBoolean("genjutsu_active")) {
			long endTime = entity.getEntityData().getLong("genjutsu_end_time");
			if (world.getTotalWorldTime() >= endTime) {
				clearGenjutsu(entity);
			}
		}
	}

	// 检查是否可以解除幻术（受到伤害时）
	public static void checkGenjutsuBreak(EntityLivingBase entity, float damage) {
		if (entity.getEntityData().getBoolean("genjutsu_active")) {
			// 受到足够伤害时有机会解除幻术
			if (damage > 2.0F && entity.world.rand.nextFloat() < 0.3F) {
				clearGenjutsu(entity);
				if (entity instanceof EntityPlayer) {
					((EntityPlayer) entity).sendMessage(new TextComponentString(TextFormatting.YELLOW + "疼痛让你从幻术中清醒过来！"));
				}
			}
		}
	}

	// 根据施术者ID查找并解除目标的忍术幻术（用于Shift+忍术3解除功能）
	public static boolean clearCasterGenjutsu(EntityLivingBase caster, World world) {
		for (java.util.Map.Entry<UUID, Integer> entry : genjutsuList.entrySet()) {
			if (entry.getValue().equals(caster.getEntityId())) {
				// 找到目标实体
				for (EntityLivingBase target : world.getEntitiesWithinAABB(EntityLivingBase.class,
					caster.getEntityBoundingBox().grow(50.0D))) {
					if (target.getUniqueID().equals(entry.getKey())) {
						clearGenjutsu(target);
						return true;
					}
				}
				break;
			}
		}
		return false;
	}
}
