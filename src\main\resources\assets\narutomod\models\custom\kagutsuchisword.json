{"credit": "Made with Blockbench", "elements": [{"from": [7, 0, 7], "to": [9, 12, 9], "rotation": {"angle": 0, "axis": "y", "origin": [8, 3.5, 8]}, "faces": {"north": {"uv": [0, 2, 16, 16], "texture": "#0"}, "east": {"uv": [0, 2, 16, 16], "texture": "#0"}, "south": {"uv": [0, 2, 16, 16], "texture": "#0"}, "west": {"uv": [0, 2, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 2, 2], "texture": "#0"}, "down": {"uv": [0, 6, 9, 16], "texture": "#0"}}}, {"from": [6, 6, 7], "to": [10, 12, 11], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 9, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 16], "texture": "#1"}, "east": {"uv": [0, 0, 15.334, 16], "texture": "#1"}, "south": {"uv": [0, 0, 15.334, 16], "texture": "#1"}, "west": {"uv": [0, 0, 16, 16], "texture": "#1"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}}}, {"from": [6.5, 8, 7.5], "to": [9.5, 18, 10.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 13, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "west": {"uv": [0, 1, 15, 16], "texture": "#0"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}}}, {"from": [6.5, 7, 8.5], "to": [9.5, 15, 11.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 13, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "west": {"uv": [1, 1, 16, 16], "texture": "#1"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}}}, {"from": [6.5, 10, 6.5], "to": [9.5, 20, 9.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 13, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "west": {"uv": [1, 1, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}}}, {"from": [6.75, 13, 7.75], "to": [9.25, 23, 10.25], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 18, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "west": {"uv": [1, 1, 16, 16], "texture": "#1"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}}}, {"from": [7, 13, 8], "to": [9, 23, 10], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 18, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "west": {"uv": [1, 1, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}}}, {"from": [7, 15, 8], "to": [9, 25, 10], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 20, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "west": {"uv": [1, 1, 16, 16], "texture": "#1"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}}}, {"from": [7, 17, 8], "to": [9, 27, 10], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 22, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "west": {"uv": [1, 1, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}}}, {"from": [7.25, 19.5, 8.25], "to": [8.75, 29.5, 9.75], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 24, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#1"}, "west": {"uv": [1, 1, 16, 16], "texture": "#1"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#1"}}}, {"from": [7.5, 22, 8.9], "to": [8.5, 32, 9.9], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 27, 8]}, "faces": {"north": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "east": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "south": {"uv": [0, 0, 15.334, 15.334], "texture": "#0"}, "west": {"uv": [1, 1, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}, "down": {"uv": [0, 0, 1.33333, 1.33333], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"translation": [0, 2, 1.5]}, "thirdperson_lefthand": {"translation": [0, 2, 1.5]}, "firstperson_righthand": {"rotation": [-15, 0, 0]}, "firstperson_lefthand": {"rotation": [-15, 0, 0]}, "gui": {"rotation": [-90, -45, -90], "translation": [-2.75, -4, 0], "scale": [0.8, 0.8, 0.8]}, "fixed": {"rotation": [90, 135, -90], "translation": [3.75, -4, 0], "scale": [0.8, 0.8, 0.8]}}}