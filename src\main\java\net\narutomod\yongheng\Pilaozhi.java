package net.narutomod.yongheng;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.init.MobEffects;
import net.minecraft.potion.PotionEffect;
import net.narutomod.procedure.ProcedureSync;

public class <PERSON>laozhi {
    // 疲劳值相关常量
    private static final String FATIGUE_KEY = "dojutsu_fatigue";
    private static final int MAX_FATIGUE = 100;
    private static final int FATIGUE_DRAIN_RATE = 1; // 每20tick消耗1点疲劳
    private static final int FATIGUE_RECOVERY_RATE = 2; // 每20tick恢复2点疲劳
    
    // 获取疲劳值
    public static int getFatigueLevel(EntityPlayer player) {
        if (!player.getEntityData().hasKey(FATIGUE_KEY)) {
            player.getEntityData().setInteger(FATIGUE_KEY, MAX_FATIGUE);
        }
        return player.getEntityData().getInteger(FATIGUE_KEY);
    }
    
    // 设置疲劳值 - 强制同步到客户端
    public static void setFatigueLevel(EntityPlayer player, int fatigue) {
        int newFatigue = Math.max(0, Math.min(MAX_FATIGUE, fatigue));
        player.getEntityData().setInteger(FATIGUE_KEY, newFatigue);
        
        // 强制同步到客户端
        if (!player.world.isRemote && player instanceof EntityPlayerMP) {
            ProcedureSync.EntityNBTTag.sendToSelf((EntityPlayerMP) player, FATIGUE_KEY, newFatigue);
        }
    }
    
    // 消耗疲劳值
    public static void drainFatigue(EntityPlayer player) {
        int currentFatigue = getFatigueLevel(player);
        int newFatigue = Math.max(0, currentFatigue - FATIGUE_DRAIN_RATE);
        setFatigueLevel(player, newFatigue);
        
        // 疲劳值为0时添加视线模糊效果
        if (newFatigue <= 0) {
            player.addPotionEffect(new PotionEffect(MobEffects.BLINDNESS, 25, 0, false, false));
            player.addPotionEffect(new PotionEffect(MobEffects.NAUSEA, 25, 0, false, false));
        }
    }
    
    // 恢复疲劳值
    public static void recoverFatigue(EntityPlayer player) {
        int currentFatigue = getFatigueLevel(player);
        if (currentFatigue < MAX_FATIGUE) {
            int newFatigue = Math.min(MAX_FATIGUE, currentFatigue + FATIGUE_RECOVERY_RATE);
            setFatigueLevel(player, newFatigue);
        }
    }
    
    // 获取最大疲劳值
    public static int getMaxFatigue() {
        return MAX_FATIGUE;
    }
}