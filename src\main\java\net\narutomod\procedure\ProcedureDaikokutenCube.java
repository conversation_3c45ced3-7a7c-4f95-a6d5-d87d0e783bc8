package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.ElementsNarutomodMod.ModElement.Tag;
import net.narutomod.entity.EntityDaikokutenCube;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;

@Tag
public class ProcedureDaikokutenCube extends ElementsNarutomodMod.ModElement {
	public ProcedureDaikokutenCube(ElementsNarutomodMod instance) {
		super(instance, 605);
	}
	
	public static void executeProcedure(EntityPlayer entity) {
		if (entity == null || entity.world == null) {
			return;
		}
		
		World world = entity.world;
		
		// 服务器端执行
		if (!world.isRemote) {
			// 射线检测
			Vec3d look = entity.getLookVec();
			Vec3d start = entity.getPositionEyes(1.0F);
			Vec3d end = start.add(look.scale(20.0D));
			RayTraceResult result = world.rayTraceBlocks(start, end);
			
			if (result != null && result.typeOfHit == RayTraceResult.Type.BLOCK) {
				int x = result.getBlockPos().getX();
				int y = result.getBlockPos().getY();
				int z = result.getBlockPos().getZ();
				
				// 在命中点上方10格处生成立方体
				EntityDaikokutenCube.DaikokutenCube cubeEntity = new EntityDaikokutenCube.DaikokutenCube(world, x + 0.5, y + 10, z + 0.5);
				cubeEntity.setOwner(entity);
				world.spawnEntity(cubeEntity);
				
				// 发送成功消息
				entity.sendMessage(new TextComponentString(
					TextFormatting.DARK_PURPLE + "大黑天立方体已召唤！"));
			} else {
				// 发送失败消息
				entity.sendMessage(new TextComponentString(
					TextFormatting.RED + "未找到有效目标！"));
			}
		}
	}
}
