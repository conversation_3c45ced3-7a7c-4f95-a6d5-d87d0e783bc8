{"credit": "Made with Blockbench", "elements": [{"from": [8, -4, 7.8], "to": [8.2, 8.1, 7.9], "rotation": {"angle": 0, "axis": "y", "origin": [0, 24, -1.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.00391, -4, 7.78034], "to": [8.20391, 8.1, 7.88034], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.20391, 23.5, 7.83034]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.04433, -4, 7.69297], "to": [8.24433, 8.1, 7.79297], "rotation": {"angle": -45, "axis": "y", "origin": [8.14433, 23.5, 7.74297]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.14344, -4, 7.61016], "to": [8.24344, 8.1, 7.81016], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.19344, 23.5, 7.71016]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.28213, -4, 7.49535], "to": [8.38213, 8.1, 7.69535], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.03213, 23.5, 7.59535]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.20137, -4, 7.59863], "to": [8.30137, 8.1, 7.79863], "rotation": {"angle": 0, "axis": "y", "origin": [8.25137, 23.5, 7.69863]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.39627, -4, 7.43084], "to": [8.49627, 8.1, 7.63084], "rotation": {"angle": -45, "axis": "y", "origin": [8.14627, 23.5, 7.53084]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.17641, -4, 7.76491], "to": [8.37641, 8.1, 7.86491], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.27641, 23.5, 7.51491]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.30273, -4, 7.8], "to": [8.50273, 8.1, 7.9], "rotation": {"angle": 0, "axis": "y", "origin": [8.40273, 23.5, 7.55]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.40602, -4, 7.88076], "to": [8.60602, 8.1, 7.98076], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.50602, 23.5, 7.63076]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.47053, -4, 7.9949], "to": [8.67053, 8.1, 8.0949], "rotation": {"angle": -45, "axis": "y", "origin": [8.57053, 23.5, 7.7449]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.23646, -4, 7.77504], "to": [8.33646, 8.1, 7.97504], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.58646, 23.5, 7.87504]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.20137, -4, 7.90137], "to": [8.30137, 8.1, 8.10137], "rotation": {"angle": 0, "axis": "y", "origin": [8.55137, 23.5, 8.00137]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.12061, -4, 8.00465], "to": [8.22061, 8.1, 8.20465], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.47061, 23.5, 8.10465]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.00647, -4, 8.06916], "to": [8.10647, 8.1, 8.26916], "rotation": {"angle": -45, "axis": "y", "origin": [8.35647, 23.5, 8.16916]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.12633, -4, 7.83509], "to": [8.32633, 8.1, 7.93509], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.22633, 23.5, 8.18509]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -5.79728, 9.9], "to": [8.6, -4.80272, 10.4], "rotation": {"angle": 45, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -5.79728, 9.9], "to": [8.6, -4.80272, 10.4], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -5.79728, 9.9], "to": [8.6, -4.80272, 10.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -5.79728, 9.9], "to": [8.6, -4.80272, 10.4], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -5.79728, 9.9], "to": [8.6, -4.80272, 10.4], "rotation": {"angle": -45, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -3.3, 7.40272], "to": [8.6, -2.8, 8.39728], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -7.8, 7.40272], "to": [8.6, -7.3, 8.39728], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -7.8, 7.40272], "to": [8.6, -7.3, 8.39728], "rotation": {"angle": 0, "axis": "y", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -3.3, 7.40272], "to": [8.6, -2.8, 8.39728], "rotation": {"angle": 0, "axis": "y", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -7.8, 7.40272], "to": [8.6, -7.3, 8.39728], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -7.8, 7.40272], "to": [8.6, -7.3, 8.39728], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, -3.3, 7.40272], "to": [8.6, -2.8, 8.39728], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, -5.3, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8, 8.1, 7.8], "to": [8.2, 20.1, 7.9], "rotation": {"angle": 0, "axis": "y", "origin": [0, -7.9, -1.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.00391, 8.1, 7.78034], "to": [8.20391, 20.1, 7.88034], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.20391, -7.4, 7.83034]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.04433, 8.1, 7.69297], "to": [8.24433, 20.1, 7.79297], "rotation": {"angle": -45, "axis": "y", "origin": [8.14433, -7.4, 7.74297]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.14344, 8.1, 7.61016], "to": [8.24344, 20.1, 7.81016], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.19344, -7.4, 7.71016]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.28213, 8.1, 7.49535], "to": [8.38213, 20.1, 7.69535], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.03213, -7.4, 7.59535]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.20137, 8.1, 7.59863], "to": [8.30137, 20.1, 7.79863], "rotation": {"angle": 0, "axis": "y", "origin": [8.25137, -7.4, 7.69863]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.39627, 8.1, 7.43084], "to": [8.49627, 20.1, 7.63084], "rotation": {"angle": -45, "axis": "y", "origin": [8.14627, -7.4, 7.53084]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.17641, 8.1, 7.76491], "to": [8.37641, 20.1, 7.86491], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.27641, -7.4, 7.51491]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.30273, 8.1, 7.8], "to": [8.50273, 20.1, 7.9], "rotation": {"angle": 0, "axis": "y", "origin": [8.40273, -7.4, 7.55]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.40602, 8.1, 7.88076], "to": [8.60602, 20.1, 7.98076], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.50602, -7.4, 7.63076]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.47053, 8.1, 7.9949], "to": [8.67053, 20.1, 8.0949], "rotation": {"angle": -45, "axis": "y", "origin": [8.57053, -7.4, 7.7449]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.23646, 8.1, 7.77504], "to": [8.33646, 20.1, 7.97504], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.58646, -7.4, 7.87504]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.20137, 8.1, 7.90137], "to": [8.30137, 20.1, 8.10137], "rotation": {"angle": 0, "axis": "y", "origin": [8.55137, -7.4, 8.00137]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.12061, 8.1, 8.00465], "to": [8.22061, 20.1, 8.20465], "rotation": {"angle": -22.5, "axis": "y", "origin": [8.47061, -7.4, 8.10465]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.00647, 8.1, 8.06916], "to": [8.10647, 20.1, 8.26916], "rotation": {"angle": -45, "axis": "y", "origin": [8.35647, -7.4, 8.16916]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"from": [8.12633, 8.1, 7.83509], "to": [8.32633, 20.1, 7.93509], "rotation": {"angle": 22.5, "axis": "y", "origin": [8.22633, -7.4, 8.18509]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 9.9], "to": [8.6, 22.49728, 10.4], "rotation": {"angle": -45, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": -45, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.8], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 9.9], "to": [8.6, 22.49728, 10.4], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 9.9], "to": [8.6, 22.49728, 10.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 9.9], "to": [8.6, 22.49728, 10.4], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": 45, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 9.9], "to": [8.6, 22.49728, 10.4], "rotation": {"angle": 45, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 19.5, 7.40272], "to": [8.6, 20, 8.39728], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 24, 7.40272], "to": [8.6, 24.5, 8.39728], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 24, 7.40272], "to": [8.6, 24.5, 8.39728], "rotation": {"angle": 0, "axis": "y", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 19.5, 7.40272], "to": [8.6, 20, 8.39728], "rotation": {"angle": 0, "axis": "y", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 24, 7.40272], "to": [8.6, 24.5, 8.39728], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 24, 7.40272], "to": [8.6, 24.5, 8.39728], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 19.5, 7.40272], "to": [8.6, 20, 8.39728], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": -45, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": 0, "axis": "y", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": -22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": 22.5, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.9, 21.50272, 5.4], "to": [8.6, 22.49728, 5.9], "rotation": {"angle": 45, "axis": "x", "origin": [8.5, 22, 7.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 7.1], "to": [8.49891, 20.6, 7.4], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 7.1], "to": [8.49891, 18.5, 7.4], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 19.3, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 7.1], "to": [8.49891, 20.6, 7.4], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 7.1], "to": [8.49891, 18.5, 7.4], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.3, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 7.1], "to": [8.49891, 20.6, 7.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 7.1], "to": [8.49891, 18.5, 7.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.3, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 7.1], "to": [8.49891, 20.6, 7.4], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 7.1], "to": [8.49891, 18.5, 7.4], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.3, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 7.1], "to": [8.49891, 20.6, 7.4], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 7.1], "to": [8.49891, 18.5, 7.4], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 19.3, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.40109, 7.1], "to": [7.5, 19.79891, 7.4], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.10109, 7.1], "to": [9.3, 19.49891, 7.4], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.3, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.10109, 7.1], "to": [7.5, 19.49891, 7.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.40109, 7.1], "to": [7.5, 19.79891, 7.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.9, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.40109, 7.1], "to": [9.3, 19.79891, 7.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.10109, 7.1], "to": [9.3, 19.49891, 7.4], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.40109, 7.1], "to": [9.3, 19.79891, 7.4], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.6, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.10109, 7.1], "to": [7.5, 19.49891, 7.4], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.3, 8.1]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 6.7], "to": [8.49891, 21, 7], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 6.7], "to": [8.49891, 18.9, 7], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 19.7, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 6.7], "to": [8.49891, 21, 7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 6.7], "to": [8.49891, 18.9, 7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.7, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 6.7], "to": [8.49891, 21, 7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 6.7], "to": [8.49891, 18.9, 7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.7, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 6.7], "to": [8.49891, 21, 7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 6.7], "to": [8.49891, 18.9, 7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.7, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 6.7], "to": [8.49891, 21, 7], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 6.7], "to": [8.49891, 18.9, 7], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 19.7, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.80109, 6.7], "to": [7.5, 20.19891, 7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.50109, 6.7], "to": [9.3, 19.89891, 7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.7, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.50109, 6.7], "to": [7.5, 19.89891, 7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.80109, 6.7], "to": [7.5, 20.19891, 7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.3, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.80109, 6.7], "to": [9.3, 20.19891, 7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.50109, 6.7], "to": [9.3, 19.89891, 7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.80109, 6.7], "to": [9.3, 20.19891, 7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.50109, 6.7], "to": [7.5, 19.89891, 7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.7, 7.7]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 6.2], "to": [8.49891, 21.4, 6.5], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 6.2], "to": [8.49891, 19.3, 6.5], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 20.1, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 6.2], "to": [8.49891, 21.4, 6.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 6.2], "to": [8.49891, 19.3, 6.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.1, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 6.2], "to": [8.49891, 21.4, 6.5], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 6.2], "to": [8.49891, 19.3, 6.5], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.1, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 6.2], "to": [8.49891, 21.4, 6.5], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 6.2], "to": [8.49891, 19.3, 6.5], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.1, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 6.2], "to": [8.49891, 21.4, 6.5], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 6.2], "to": [8.49891, 19.3, 6.5], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 20.1, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 20.20109, 6.2], "to": [7.5, 20.59891, 6.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.90109, 6.2], "to": [9.3, 20.29891, 6.5], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.1, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.90109, 6.2], "to": [7.5, 20.29891, 6.5], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 20.20109, 6.2], "to": [7.5, 20.59891, 6.5], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.7, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 20.20109, 6.2], "to": [9.3, 20.59891, 6.5], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.90109, 6.2], "to": [9.3, 20.29891, 6.5], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 20.20109, 6.2], "to": [9.3, 20.59891, 6.5], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.4, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.90109, 6.2], "to": [7.5, 20.29891, 6.5], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.1, 7.2]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 8.4], "to": [8.49891, 20.6, 8.7], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 8.4], "to": [8.49891, 18.5, 8.7], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 19.3, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 8.4], "to": [8.49891, 20.6, 8.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 8.4], "to": [8.49891, 18.5, 8.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.3, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 8.4], "to": [8.49891, 20.6, 8.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 8.4], "to": [8.49891, 18.5, 8.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.3, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 8.4], "to": [8.49891, 20.6, 8.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 8.4], "to": [8.49891, 18.5, 8.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.3, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.4, 8.4], "to": [8.49891, 20.6, 8.7], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.3, 8.4], "to": [8.49891, 18.5, 8.7], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 19.3, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.40109, 8.4], "to": [7.5, 19.79891, 8.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.10109, 8.4], "to": [9.3, 19.49891, 8.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.3, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.10109, 8.4], "to": [7.5, 19.49891, 8.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.40109, 8.4], "to": [7.5, 19.79891, 8.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.9, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.40109, 8.4], "to": [9.3, 19.79891, 8.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.10109, 8.4], "to": [9.3, 19.49891, 8.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.40109, 8.4], "to": [9.3, 19.79891, 8.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.6, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.10109, 8.4], "to": [7.5, 19.49891, 8.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.3, 9.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 8.9], "to": [8.49891, 21, 9.2], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 8.9], "to": [8.49891, 18.9, 9.2], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 19.7, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 8.9], "to": [8.49891, 21, 9.2], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 8.9], "to": [8.49891, 18.9, 9.2], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.7, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 8.9], "to": [8.49891, 21, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 8.9], "to": [8.49891, 18.9, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 19.7, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 8.9], "to": [8.49891, 21, 9.2], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 8.9], "to": [8.49891, 18.9, 9.2], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.7, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 20.8, 8.9], "to": [8.49891, 21, 9.2], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 18.7, 8.9], "to": [8.49891, 18.9, 9.2], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 19.7, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.80109, 8.9], "to": [7.5, 20.19891, 9.2], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.50109, 8.9], "to": [9.3, 19.89891, 9.2], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 19.7, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.50109, 8.9], "to": [7.5, 19.89891, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.80109, 8.9], "to": [7.5, 20.19891, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.3, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.80109, 8.9], "to": [9.3, 20.19891, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.50109, 8.9], "to": [9.3, 19.89891, 9.2], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.80109, 8.9], "to": [9.3, 20.19891, 9.2], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.50109, 8.9], "to": [7.5, 19.89891, 9.2], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 19.7, 9.9]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 9.4], "to": [8.49891, 21.4, 9.7], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 9.4], "to": [8.49891, 19.3, 9.7], "rotation": {"angle": -45, "axis": "z", "origin": [8.3, 20.1, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 9.4], "to": [8.49891, 21.4, 9.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 9.4], "to": [8.49891, 19.3, 9.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.1, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 9.4], "to": [8.49891, 21.4, 9.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 9.4], "to": [8.49891, 19.3, 9.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.1, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 9.4], "to": [8.49891, 21.4, 9.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 9.4], "to": [8.49891, 19.3, 9.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.1, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 21.2, 9.4], "to": [8.49891, 21.4, 9.7], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [8.10109, 19.1, 9.4], "to": [8.49891, 19.3, 9.7], "rotation": {"angle": 45, "axis": "z", "origin": [8.3, 20.1, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 20.20109, 9.4], "to": [7.5, 20.59891, 9.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.90109, 9.4], "to": [9.3, 20.29891, 9.7], "rotation": {"angle": -22.5, "axis": "z", "origin": [8.3, 20.1, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.90109, 9.4], "to": [7.5, 20.29891, 9.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 20.20109, 9.4], "to": [7.5, 20.59891, 9.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.7, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 20.20109, 9.4], "to": [9.3, 20.59891, 9.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 19.90109, 9.4], "to": [9.3, 20.29891, 9.7], "rotation": {"angle": 0, "axis": "y", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [9.1, 20.20109, 9.4], "to": [9.3, 20.59891, 9.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.4, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}, {"name": "hexadecagon", "from": [7.3, 19.90109, 9.4], "to": [7.5, 20.29891, 9.7], "rotation": {"angle": 22.5, "axis": "z", "origin": [8.3, 20.1, 10.4]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "up": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [0, 0, 16, 16], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"rotation": [30, 0, 0], "translation": [-0.5, 2, 3.5], "scale": [2, 1.5, 1.6]}, "thirdperson_lefthand": {"rotation": [30, 0, 0], "translation": [0.5, 2, 3.5], "scale": [2, 1.5, 1.6]}, "firstperson_righthand": {"translation": [-0.5, -5, 3.5], "scale": [2, 1.5, 1.6]}, "firstperson_lefthand": {"translation": [-0.5, -5, 3.5], "scale": [2, 1.5, 1.6]}, "ground": {"translation": [0, 20, 0], "scale": [2, 1.7, 1.8]}, "gui": {"rotation": [112.12, 46.58, -106.45], "translation": [-0.25, -0.5, 0], "scale": [1.75, 1.2, 1.75]}, "head": {"rotation": [-15, 0, 0], "translation": [-10, -9.75, -5], "scale": [2, 2.3, 2.3]}, "fixed": {"rotation": [-90, 90, 90], "translation": [0.5, 0, 0], "scale": [2, 1.5, 1.6]}}, "groups": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [{"name": "group", "origin": [8, 8, 8], "color": 7, "children": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 5, "children": [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]}]}, {"name": "group", "origin": [8, 8, 8], "color": 0, "children": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [{"name": "group", "origin": [8, 8, 8], "color": 7, "children": [28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 5, "children": [44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64]}]}]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 0, "children": [65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 0, "children": [83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 0, "children": [101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 0, "children": [119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 0, "children": [137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154]}, {"name": "hexadecagon", "origin": [8, 8, 8], "color": 0, "children": [155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172]}]}]}]}