package net.narutomod.jiami;

import net.minecraftforge.fml.common.FMLCommonHandler;
import java.io.*;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Properties;

/**
 * 许可证密钥验证器 - 优化版
 * 提供安全的一次性验证机制
 */
public class KeyValidator {

    // 配置常量
    private static final String CONFIG_FILE = "config/narutomod_license.properties";
    private static final String LICENSE_KEY = "license_key";
    private static final String SERVER_ID_KEY = "server_id";

    // 安全常量 - 实际部署时应该使用更复杂的哈希
    private static final String EXPECTED_KEY_HASH = "f813d8e95f2a70a836917653833900ec6de4a27c";

    // 验证状态
    private static boolean validationAttempted = false;
    private static String lastValidationError = null;

    /**
     * 验证服务器许可证密钥
     * @return 验证是否成功
     */
    public static synchronized boolean validateServerKey() {
        if (validationAttempted) {
            // 避免重复验证，返回上次结果
            return lastValidationError == null;
        }

        validationAttempted = true;
        lastValidationError = null;

        try {
            // 1. 检查配置文件
            File configFile = new File(CONFIG_FILE);
            if (!configFile.exists()) {
                lastValidationError = "配置文件不存在";
                createDefaultConfig();
                logError("未找到许可证配置文件，已创建默认配置: " + CONFIG_FILE);
                logError("请在配置文件中设置正确的许可证密钥！");
                return false;
            }

            // 2. 读取配置
            Properties props = loadConfiguration(configFile);
            if (props == null) {
                lastValidationError = "配置文件读取失败";
                return false;
            }

            // 3. 验证密钥
            String inputKey = props.getProperty(LICENSE_KEY, "").trim();
            if (inputKey.isEmpty() || "请在此处输入您的许可证密钥".equals(inputKey)) {
                lastValidationError = "许可证密钥未设置";
                logError("许可证密钥为空或未修改，请设置正确的密钥！");
                return false;
            }

            // 4. 执行哈希验证
            boolean isValid = validateKeyHash(inputKey);

            if (isValid) {
                // 5. 生成或验证服务器ID
                ensureServerIdentity(props, configFile);
                logSuccess("许可证验证成功！服务器已授权运行");
                return true;
            } else {
                lastValidationError = "密钥无效";
                logError("许可证验证失败！密钥无效！");
                logError("请联系模组作者QQ1340198249获取正确的许可证密钥");
                return false;
            }

        } catch (Exception e) {
            lastValidationError = "验证过程异常: " + e.getMessage();
            logError("许可证验证过程中发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 读取配置文件
     */
    private static Properties loadConfiguration(File configFile) {
        try {
            Properties props = new Properties();
            try (FileInputStream fis = new FileInputStream(configFile)) {
                props.load(fis);
            }
            return props;
        } catch (Exception e) {
            logError("读取配置文件失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证密钥哈希
     */
    private static boolean validateKeyHash(String inputKey) {
        try {
            String keyHash = hashKey(inputKey);
            return EXPECTED_KEY_HASH.equals(keyHash);
        } catch (Exception e) {
            logError("密钥哈希验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 确保服务器身份标识
     */
    private static void ensureServerIdentity(Properties props, File configFile) {
        try {
            String serverId = props.getProperty(SERVER_ID_KEY);
            if (serverId == null || serverId.trim().isEmpty()) {
                // 生成新的服务器ID
                serverId = generateServerId();
                props.setProperty(SERVER_ID_KEY, serverId);

                // 保存配置
                try (FileOutputStream fos = new FileOutputStream(configFile)) {
                    props.store(fos, "NarutoMod 服务器许可证配置 - 自动更新于 " + new java.util.Date());
                }

                logInfo("已生成服务器身份标识: " + serverId.substring(0, 8) + "...");
            }
        } catch (Exception e) {
            logError("服务器身份标识处理失败: " + e.getMessage());
        }
    }

    /**
     * 生成服务器ID
     */
    private static String generateServerId() {
        try {
            SecureRandom random = new SecureRandom();
            byte[] bytes = new byte[16];
            random.nextBytes(bytes);

            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            // 备用方案
            return String.valueOf(System.currentTimeMillis()) + "_" +
                   String.valueOf(System.nanoTime()).substring(0, 8);
        }
    }

    /**
     * 创建默认配置文件
     */
    private static void createDefaultConfig() {
        try {
            // 确保config目录存在
            File configDir = new File("config");
            if (!configDir.exists()) {
                configDir.mkdirs();
            }

            Properties props = new Properties();
            props.setProperty(LICENSE_KEY, "请在此处输入您的许可证密钥");

            try (FileOutputStream fos = new FileOutputStream(CONFIG_FILE)) {
                // 写入配置文件头部注释
                fos.write("# NarutoMod 服务器许可证配置文件\n".getBytes("UTF-8"));
                fos.write("# 请联系模组作者QQ1340198249获取正确的许可证密钥\n".getBytes("UTF-8"));
                fos.write("# 作者: NarutoMod Team\n".getBytes("UTF-8"));
                fos.write("# 生成时间: ".getBytes("UTF-8"));
                fos.write(new java.util.Date().toString().getBytes("UTF-8"));
                fos.write("\n\n".getBytes("UTF-8"));

                props.store(fos, null);
            }

        } catch (Exception e) {
            logError("创建默认配置文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 计算密钥哈希值
     */
    private static String hashKey(String key) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] hash = md.digest(key.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b : hash) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("密钥哈希计算失败", e);
        }
    }

    /**
     * 关闭服务器
     */
    public static void shutdownServer(String reason) {
        logError("========================================");
        logError("服务器即将关闭: " + reason);
        logError("请联系模组作者QQ1340198249获取正确的许可证！");
        logError("========================================");

        // 延迟5秒后关闭服务器，给日志输出和清理时间
        new Thread(() -> {
            try {
                for (int i = 5; i > 0; i--) {
                    System.err.println("[NarutoMod] 服务器将在 " + i + " 秒后关闭...");
                    Thread.sleep(1000);
                }

                System.err.println("[NarutoMod] 正在关闭服务器...");
                FMLCommonHandler.instance().exitJava(1, false);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("[NarutoMod] 强制关闭服务器");
                System.exit(1);
            }
        }, "NarutoMod-License-Shutdown").start();
    }

    /**
     * 日志输出 - 错误信息
     */
    private static void logError(String message) {
        System.err.println("[NarutoMod] " + message);
    }

    /**
     * 日志输出 - 成功信息
     */
    private static void logSuccess(String message) {
        System.out.println("[NarutoMod] " + message);
    }

    /**
     * 日志输出 - 一般信息
     */
    private static void logInfo(String message) {
        System.out.println("[NarutoMod] " + message);
    }
}