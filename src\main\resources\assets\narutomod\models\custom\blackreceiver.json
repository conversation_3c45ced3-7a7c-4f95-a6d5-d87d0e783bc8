{"credit": "Made with Blockbench", "ambientocclusion": false, "elements": [{"from": [7, 7, 1], "to": [9, 9, 15], "rotation": {"angle": 0, "axis": "z", "origin": [8, 6, 8]}, "faces": {"north": {"uv": [6, 0, 4, 2], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 270, "texture": "#0"}, "south": {"uv": [4, 2, 2, 0], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 90, "texture": "#0"}, "up": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}, "down": {"uv": [6, 2, 8, 16], "texture": "#0"}}}, {"from": [7, 7, -1.6], "to": [9, 7, 1], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 7, 1]}, "faces": {"down": {"uv": [8, 12, 16, 0], "texture": "#0"}}}, {"from": [7, 9, -1.6], "to": [9, 9, 1], "rotation": {"angle": -22.5, "axis": "x", "origin": [8, 9, 1]}, "faces": {"up": {"uv": [8, 0, 16, 12], "texture": "#0"}}}, {"from": [9, 7, -1.6], "to": [9, 9, 1], "rotation": {"angle": 22.5, "axis": "y", "origin": [9, 8, 1]}, "faces": {"east": {"uv": [8, 0, 16, 12], "rotation": 90, "texture": "#0"}}}, {"from": [7, 7, -1.6], "to": [7, 9, 1], "rotation": {"angle": -22.5, "axis": "y", "origin": [7, 8, 1]}, "faces": {"west": {"uv": [8, 12, 16, 0], "rotation": 90, "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"translation": [0, -2, -5]}, "thirdperson_lefthand": {"translation": [0, -2, -5]}, "firstperson_righthand": {"translation": [0, -2, -6]}, "firstperson_lefthand": {"translation": [0, -2, -6]}, "gui": {"rotation": [70, -45, 0], "scale": [1.25, 1.25, 1.25]}, "head": {"scale": [2, 2, 2]}, "fixed": {"rotation": [90, 45, 0], "translation": [0, 0, 0.5], "scale": [1.4, 1.4, 1.4]}}}