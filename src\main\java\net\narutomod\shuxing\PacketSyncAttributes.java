package net.narutomod.shuxing;

import io.netty.buffer.ByteBuf;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

public class PacketSyncAttributes implements IMessage {
    private int availablePoints;
    private int[] attributeLevels;
    
    public PacketSyncAttributes() {
        this.attributeLevels = new int[AttributeManager.ATTRIBUTE_NAMES.length];
    }
    
    public PacketSyncAttributes(int availablePoints, int[] attributeLevels) {
        this.availablePoints = availablePoints;
        this.attributeLevels = attributeLevels.clone();
    }
    
    @Override
    public void fromBytes(ByteBuf buf) {
        this.availablePoints = buf.readInt();
        for (int i = 0; i < this.attributeLevels.length; i++) {
            this.attributeLevels[i] = buf.readInt();
        }
    }
    
    @Override
    public void toBytes(ByteBuf buf) {
        buf.writeInt(this.availablePoints);
        for (int level : this.attributeLevels) {
            buf.writeInt(level);
        }
    }
    
    public static class Handler implements IMessageHandler<PacketSyncAttributes, IMessage> {
        @Override
        @SideOnly(Side.CLIENT)
        public IMessage onMessage(PacketSyncAttributes message, MessageContext ctx) {
            Minecraft.getMinecraft().addScheduledTask(() -> {
                if (Minecraft.getMinecraft().player != null) {
                    AttributeEnhancement attr = AttributeEnhancement.get(Minecraft.getMinecraft().player);
                    if (attr != null) {
                        attr.setAvailablePoints(message.availablePoints);
                        for (int i = 0; i < message.attributeLevels.length; i++) {
                            attr.setAttributeLevel(i, message.attributeLevels[i]);
                        }
                    }
                }
            });
            return null;
        }
    }
}
