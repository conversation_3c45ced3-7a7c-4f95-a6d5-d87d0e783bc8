package net.narutomod.shuxing;

import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.common.MinecraftForge;
import net.narutomod.ElementsNarutomodMod;

@ElementsNarutomodMod.ModElement.Tag
public class AttributeSystemElement extends ElementsNarutomodMod.ModElement {

    public AttributeSystemElement(ElementsNarutomodMod instance) {
        super(instance, 999); // 高优先级确保早期加载
    }

    @Override
    public void preInit(FMLPreInitializationEvent event) {
        // 属性系统已在主mod类中初始化，这里只做验证
        System.out.println("属性系统元素预初始化完成");
    }

    @Override
    public void init(FMLInitializationEvent event) {
        // 确保忍术伤害处理器注册
        MinecraftForge.EVENT_BUS.register(new JutsuDamageHandler());
        System.out.println("忍术伤害处理器注册完成");
    }

    @Override
    public void serverLoad(FMLServerStartingEvent event) {
        // 命令已在主mod类中注册
        System.out.println("属性系统服务器加载完成");
    }
}