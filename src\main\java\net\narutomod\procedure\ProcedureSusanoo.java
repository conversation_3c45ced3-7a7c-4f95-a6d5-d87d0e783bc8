package net.narutomod.procedure;

import net.minecraftforge.event.entity.EntityTravelToDimensionEvent;
import net.minecraftforge.fml.common.gameevent.PlayerEvent;
import net.minecraftforge.fml.common.network.FMLNetworkEvent;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

import net.minecraft.world.World;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.Entity;
import net.minecraft.potion.PotionEffect;
import net.minecraft.init.MobEffects;
import net.minecraft.item.ItemStack;
import net.minecraft.network.NetHandlerPlayServer;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.inventory.EntityEquipmentSlot;

import net.narutomod.potion.PotionFeatherFalling;
import net.narutomod.item.ItemRinnegan;
import net.narutomod.item.ItemSharingan;
import net.narutomod.item.ItemMangekyoSharinganEternal;
import net.narutomod.entity.EntitySusanooWinged;
import net.narutomod.entity.EntitySusanooSkeleton;
import net.narutomod.entity.EntitySusanooClothed;
import net.narutomod.entity.EntitySusanooBase;
import net.narutomod.Chakra;
import net.narutomod.PlayerTracker;
import net.narutomod.NarutomodModVariables;
import net.narutomod.ElementsNarutomodMod;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureSusanoo extends ElementsNarutomodMod.ModElement {
	private static final String SUMMONED_SUSANOO = "summonedSusanooID";
	public static final double BASE_CHAKRA_USAGE = 500d;

	public ProcedureSusanoo(ElementsNarutomodMod instance) {
		super(instance, 168);
	}

	@Override
	public void init(FMLInitializationEvent event) {
		MinecraftForge.EVENT_BUS.register(new PlayerHook());
	}

	public static int getSummonedSusanooId(Entity entity) {
		return entity.getEntityData().getInteger(SUMMONED_SUSANOO);
	}

	public static boolean isActivated(Entity entity) {
		Entity susanoo = entity.world.getEntityByID(getSummonedSusanooId(entity));
		return susanoo instanceof EntitySusanooBase && susanoo.isEntityAlive();
	}

	public static void execute(EntityPlayer player) {
		World world = player.world;
		boolean flag = (player.isCreative() || ProcedureUtils.hasAnyItemOfSubtype(player, ItemRinnegan.Base.class));
		ItemStack helmet = player.inventory.armorInventory.get(3);
		if (!player.getEntityData().getBoolean("susanoo_activated")) {
			if (!ItemSharingan.isBlinded(helmet) && PlayerTracker.getBattleXp(player) >= EntitySusanooBase.BXP_REQUIRED_L0
			 && Chakra.pathway(player).consume(BASE_CHAKRA_USAGE)) {
				player.getEntityData().setBoolean("susanoo_activated", true);
				player.getEntityData().setDouble("susanoo_cd", NarutomodModVariables.world_tick + 2400.0D);
				EntitySusanooBase entityCustom = new EntitySusanooSkeleton.EntityCustom(player);
				world.spawnEntity(entityCustom);
				player.getEntityData().setInteger(SUMMONED_SUSANOO, entityCustom.getEntityId());
			}
		} else {
			double cooldown = player.getEntityData().getDouble("susanoo_ticks") * 0.25d;
			cooldown *= ProcedureUtils.getCooldownModifier(player);
			player.getEntityData().removeTag("susanoo_activated");
			player.getEntityData().removeTag("susanoo_ticks");
			Entity entitySpawned = world.getEntityByID(getSummonedSusanooId(player));
			player.getEntityData().removeTag(SUMMONED_SUSANOO);
			if (entitySpawned instanceof EntitySusanooBase) {
				entitySpawned.setDead();
				if (!flag && (!(helmet.getItem() instanceof ItemSharingan.Base) || !((ItemSharingan.Base)helmet.getItem()).isEternal())) {
					player.addPotionEffect(new PotionEffect(MobEffects.WEAKNESS, (int)cooldown, 3));
					player.addPotionEffect(new PotionEffect(MobEffects.NAUSEA, (int)cooldown, 2));
				}
				player.addPotionEffect(new PotionEffect(PotionFeatherFalling.potion, 60, 5));
			}
		}
	}
	
	public static void executeProcedure(java.util.Map<String, Object> dependencies) {
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure ProcedureSusanoo!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure ProcedureSusanoo!");
			return;
		}
		Entity entity = (Entity) dependencies.get("entity");
		if (!(entity instanceof EntityPlayer)) {
			System.err.println("Unauthorized calling of procedure ProcedureSusanoo! (entity not player)");
			return;
		}
		execute((EntityPlayer)entity);
	}

	public static void upgrade(EntityPlayer player) {
		Entity susanoo = player.getRidingEntity();
		double playerXp = PlayerTracker.getBattleXp(player);
		if (susanoo instanceof EntitySusanooBase) {
			if (susanoo instanceof EntitySusanooSkeleton.EntityCustom) {
				boolean fullBody = ((EntitySusanooSkeleton.EntityCustom)susanoo).isFullBody();
				if (!fullBody && playerXp >= EntitySusanooBase.BXP_REQUIRED_L1) {
					if (Chakra.pathway(player).consume(BASE_CHAKRA_USAGE)) {
						changeEntity(player, susanoo, new EntitySusanooSkeleton.EntityCustom(player, true));
					}
				} else if (fullBody && playerXp >= EntitySusanooBase.BXP_REQUIRED_L2) {
					if (Chakra.pathway(player).consume(BASE_CHAKRA_USAGE)) {
						changeEntity(player, susanoo, new EntitySusanooClothed.EntityCustom(player, false));
					}
				}
			} else if (susanoo instanceof EntitySusanooClothed.EntityCustom) {
				boolean hasLegs = ((EntitySusanooClothed.EntityCustom)susanoo).hasLegs();
				if (hasLegs && playerXp >= EntitySusanooBase.BXP_REQUIRED_L4) {
					// 检查是否可以升级到完全体(EntitySusanooWinged)
					if (canUpgradeToWingedForm(player)) {
						if (Chakra.pathway(player).consume(BASE_CHAKRA_USAGE)) {
							changeEntity(player, susanoo, new EntitySusanooWinged.EntityCustom(player));
						}
					} else {
						// 如果不能升级到完全体，给玩家提示
						if (!player.world.isRemote) {
							player.sendStatusMessage(new net.minecraft.util.text.TextComponentString(
								"§c需要进化到永恒万花筒写轮眼才能使用须佐能乎完全体！"), false);
						}
					}
				} else if (!hasLegs && playerXp >= EntitySusanooBase.BXP_REQUIRED_L3) {
					if (Chakra.pathway(player).consume(BASE_CHAKRA_USAGE)) {
						changeEntity(player, susanoo, new EntitySusanooClothed.EntityCustom(player, true));
					}
				}
			}
		}
	}

	/**
	 * 检查玩家是否可以升级到须佐能乎完全体(翼型)
	 * 只有永恒万花筒写轮眼或创造模式玩家可以使用完全体
	 * @param player 玩家
	 * @return 是否可以升级到完全体
	 */
	public static boolean canUpgradeToWingedForm(EntityPlayer player) {
		// 创造模式玩家可以使用完全体
		if (player.isCreative()) {
			return true;
		}

		// 检查是否有轮回眼（轮回眼可以使用完全体）
		if (ProcedureUtils.hasAnyItemOfSubtype(player, ItemRinnegan.Base.class)) {
			return true;
		}

		// 检查头盔是否是永恒万花筒写轮眼
		ItemStack helmet = player.getItemStackFromSlot(EntityEquipmentSlot.HEAD);
		if (helmet.getItem() instanceof ItemSharingan.Base) {
			ItemSharingan.Base sharinganItem = (ItemSharingan.Base) helmet.getItem();
			// 只有永恒万花筒写轮眼可以使用完全体
			return sharinganItem.isEternal();
		}

		return false;
	}

	private static void changeEntity(EntityPlayer player, Entity oldSusanoo, Entity newSusanoo) {
		oldSusanoo.setDead();
		newSusanoo.copyLocationAndAnglesFrom(oldSusanoo);
		player.world.spawnEntity(newSusanoo);
		player.getEntityData().setInteger(SUMMONED_SUSANOO, newSusanoo.getEntityId());
	}

	public class PlayerHook {
		private void checkAndRemove(EntityPlayer entity) {
			if (entity.getEntityData().getBoolean("susanoo_activated")) {
				execute(entity);
			}
		}

		@SubscribeEvent
		public void onPlayerChangeDimension(EntityTravelToDimensionEvent event) {
			if (event.getEntity() instanceof EntityPlayer)
				this.checkAndRemove((EntityPlayer)event.getEntity());
		}

		@SubscribeEvent
		public void onPlayerLeave(PlayerEvent.PlayerLoggedOutEvent event) {
			this.checkAndRemove(event.player);
		}

		@SubscribeEvent
		public void onServerDisconnect(FMLNetworkEvent.ServerDisconnectionFromClientEvent event) {
			this.checkAndRemove(((NetHandlerPlayServer)event.getHandler()).player);
		}
	}

}
