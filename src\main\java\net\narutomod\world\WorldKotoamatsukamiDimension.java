package net.narutomod.world;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.world.biome.BiomeKotoamatsukami;

import net.minecraft.world.WorldProvider;
import net.minecraft.world.DimensionType;
import net.minecraft.world.gen.IChunkGenerator;
import net.minecraft.world.biome.BiomeProvider;
import net.minecraft.world.biome.BiomeProviderSingle;
import net.minecraft.util.math.Vec3d;
import net.minecraft.entity.Entity;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.client.IRenderHandler;

@ElementsNarutomodMod.ModElement.Tag
public class WorldKotoamatsukamiDimension extends ElementsNarutomodMod.ModElement {
	
	public static DimensionType KOTOAMATSUKAMI;
	
	public WorldKotoamatsukamiDimension(ElementsNarutomodMod instance) {
		super(instance, 605);
	}

	@Override
	public void initElements() {
		// 维度在静态初始化块中已经创建
	}

	@Override
	public void init(net.minecraftforge.fml.common.event.FMLInitializationEvent event) {
		net.minecraftforge.common.DimensionManager.registerDimension(getDimensionId(), KOTOAMATSUKAMI);
	}

	public static int getDimensionId() {
		return 449; // 使用与源项目相同的ID
	}

	static {
		KOTOAMATSUKAMI = DimensionType.register("Kotoamatsukami", "_kotoamatsukami", getDimensionId(), 
			WorldProviderKotoamatsukami.class, false);
	}

	public static class WorldProviderKotoamatsukami extends WorldProvider {
		
		@Override
		public DimensionType getDimensionType() {
			return KOTOAMATSUKAMI;
		}

		@Override
		public IChunkGenerator createChunkGenerator() {
			return new ChunkGeneratorKotoamatsukami(this.world, this.world.getSeed());
		}

		@Override
		protected void init() {
			this.biomeProvider = new BiomeProviderSingle(BiomeKotoamatsukami.biome);
			this.hasSkyLight = true;
		}

		@Override
		public float getSunBrightness(float par1) {
			return 0.3F;
		}

		@Override
		public long getWorldTime() {
			return 1500L; // 固定时间，营造特殊氛围
		}

		@Override
		@SideOnly(Side.CLIENT)
		public IRenderHandler getCloudRenderer() {
			return new IRenderHandler() {
				@Override
				public void render(float partialTicks, net.minecraft.client.multiplayer.WorldClient world, 
					net.minecraft.client.Minecraft mc) {
					// 不渲染云层
				}
			};
		}

		@Override
		public float getCloudHeight() {
			return 0.0F;
		}

		@Override
		public boolean canRespawnHere() {
			return false;
		}

		@Override
		public boolean isSurfaceWorld() {
			return super.isSurfaceWorld();
		}

		@Override
		public boolean canCoordinateBeSpawn(int x, int z) {
			return true;
		}

		@Override
		@SideOnly(Side.CLIENT)
		public IRenderHandler getSkyRenderer() {
			return new KotoamatsukamiSkyRenderer();
		}

		@Override
		public Vec3d getSkyColor(Entity cameraEntity, float partialTicks) {
			return new Vec3d(139.0D / 255.0D, 0.0D, 0.0D); // 红色天空
		}

		@Override
		public Vec3d getFogColor(float p_76562_1_, float p_76562_2_) {
			return new Vec3d(128.0D / 255.0D, 0.0D, 0.0D); // 红色雾气
		}
	}

	@SideOnly(Side.CLIENT)
	public static class KotoamatsukamiSkyRenderer extends IRenderHandler {
		
		private static final net.minecraft.util.ResourceLocation sunTexture = 
			new net.minecraft.util.ResourceLocation("narutomod", "textures/environment/kotoamatsukami/sun.png");
		
		private int starGLCallList = net.minecraft.client.renderer.GLAllocation.generateDisplayLists(3);
		private int glSkyList;
		private int glSkyList2;
		private float sunSize = 35.0F;

		public KotoamatsukamiSkyRenderer() {
			net.minecraft.client.renderer.Tessellator tessellator = net.minecraft.client.renderer.Tessellator.getInstance();
			net.minecraft.client.renderer.BufferBuilder worldRenderer = tessellator.getBuffer();
			
			this.glSkyList = this.starGLCallList + 1;
			org.lwjgl.opengl.GL11.glNewList(this.glSkyList, org.lwjgl.opengl.GL11.GL_COMPILE);
			
			// 渲染天空盒
			for (int j = -384; j <= 384; j += 64) {
				for (int l = -384; l <= 384; l += 64) {
					worldRenderer.begin(7, net.minecraft.client.renderer.vertex.DefaultVertexFormats.POSITION);
					worldRenderer.pos(j + 0, 16.0F, l + 0).endVertex();
					worldRenderer.pos(j + 64, 16.0F, l + 0).endVertex();
					worldRenderer.pos(j + 64, 16.0F, l + 64).endVertex();
					worldRenderer.pos(j + 0, 16.0F, l + 64).endVertex();
					tessellator.draw();
				}
			}
			
			org.lwjgl.opengl.GL11.glEndList();
		}

		@Override
		public void render(float partialTicks, net.minecraft.client.multiplayer.WorldClient world, 
			net.minecraft.client.Minecraft mc) {
			
			net.minecraft.client.renderer.Tessellator tessellator = net.minecraft.client.renderer.Tessellator.getInstance();
			net.minecraft.client.renderer.BufferBuilder worldRenderer = tessellator.getBuffer();
			
			org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_TEXTURE_2D);
			net.minecraft.client.renderer.GlStateManager.disableFog();
			org.lwjgl.opengl.GL11.glColor3f(1.0F, 1.0F, 1.0F);
			org.lwjgl.opengl.GL11.glDepthMask(false);
			org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_FOG);
			org.lwjgl.opengl.GL11.glColor3f(0.0F, 0.0F, 0.0F);
			org.lwjgl.opengl.GL11.glCallList(this.glSkyList);
			org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_FOG);
			org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_ALPHA_TEST);
			org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_BLEND);
			org.lwjgl.opengl.GL11.glBlendFunc(org.lwjgl.opengl.GL11.GL_SRC_ALPHA, org.lwjgl.opengl.GL11.GL_ONE_MINUS_SRC_ALPHA);
			net.minecraft.client.renderer.RenderHelper.disableStandardItemLighting();
			
			// 渲染太阳
			org.lwjgl.opengl.GL11.glPushMatrix();
			org.lwjgl.opengl.GL11.glColor4f(1.0F, 1.0F, 1.0F, 1.0F);
			org.lwjgl.opengl.GL11.glRotatef(-90.0F, 0.0F, 1.0F, 0.0F);
			org.lwjgl.opengl.GL11.glRotatef(world.getCelestialAngle(partialTicks) * 360.0F, 1.0F, 0.0F, 0.0F);
			org.lwjgl.opengl.GL11.glBlendFunc(org.lwjgl.opengl.GL11.GL_SRC_ALPHA, org.lwjgl.opengl.GL11.GL_ONE_MINUS_SRC_ALPHA);
			
			org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_TEXTURE_2D);
			org.lwjgl.opengl.GL11.glBlendFunc(org.lwjgl.opengl.GL11.GL_SRC_ALPHA, org.lwjgl.opengl.GL11.GL_ONE);
			org.lwjgl.opengl.GL11.glColor4f(1.0F, 1.0F, 1.0F, 1.0F);
			float var12 = this.sunSize;
			
			mc.getTextureManager().bindTexture(sunTexture);
			worldRenderer.begin(7, net.minecraft.client.renderer.vertex.DefaultVertexFormats.POSITION_TEX);
			worldRenderer.pos(-var12, 90.0D, -var12).tex(0.0D, 0.0D).endVertex();
			worldRenderer.pos(var12, 90.0D, -var12).tex(1.0D, 0.0D).endVertex();
			worldRenderer.pos(var12, 90.0D, var12).tex(1.0D, 1.0D).endVertex();
			worldRenderer.pos(-var12, 90.0D, var12).tex(0.0D, 1.0D).endVertex();
			tessellator.draw();
			
			org.lwjgl.opengl.GL11.glPopMatrix();
			
			org.lwjgl.opengl.GL11.glColor4f(1.0F, 1.0F, 1.0F, 1.0F);
			org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_BLEND);
			org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_ALPHA_TEST);
			org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_FOG);
			org.lwjgl.opengl.GL11.glPopMatrix();
			
			org.lwjgl.opengl.GL11.glDisable(org.lwjgl.opengl.GL11.GL_TEXTURE_2D);
			org.lwjgl.opengl.GL11.glColor3f(0.0F, 0.0F, 0.0F);
			org.lwjgl.opengl.GL11.glColor3f(0.2734375F, 0.2734375F, 0.2734375F);
			
			net.minecraft.client.renderer.GlStateManager.enableFog();
			org.lwjgl.opengl.GL11.glEnable(org.lwjgl.opengl.GL11.GL_TEXTURE_2D);
			org.lwjgl.opengl.GL11.glDepthMask(true);
		}
	}
}
