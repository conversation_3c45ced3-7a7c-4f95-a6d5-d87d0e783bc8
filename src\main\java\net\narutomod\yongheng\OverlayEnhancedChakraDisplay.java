package net.narutomod.yongheng;

import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.eventhandler.EventPriority;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.client.event.RenderGameOverlayEvent;

import net.minecraft.util.ResourceLocation;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.client.gui.GuiIngame;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.OpenGlHelper;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.PlayerTracker;
import net.narutomod.Chakra;
import net.narutomod.yongheng.Pilaozhi;

@ElementsNarutomodMod.ModElement.Tag
public class OverlayEnhancedChakraDisplay extends ElementsNarutomodMod.ModElement {
    
    public OverlayEnhancedChakraDisplay(ElementsNarutomodMod instanceIn) {
        super(instanceIn, 398);
    }
    
    @SideOnly(Side.CLIENT)
    @Override
    public void init(FMLInitializationEvent event) {
        MinecraftForge.EVENT_BUS.register(new GUIRenderEventClass());
    }
    
    public static class GUIRenderEventClass {
        @SubscribeEvent(priority = EventPriority.NORMAL)
        @SideOnly(Side.CLIENT)
        public void eventHandler(RenderGameOverlayEvent event) {
            if (!event.isCancelable() && event.getType() == RenderGameOverlayEvent.ElementType.HELMET) {
                Minecraft mc = Minecraft.getMinecraft();
                if (PlayerTracker.isNinja(mc.player) && Chakra.isInitialized(mc.player)) {
                    int sWidth = event.getResolution().getScaledWidth();
                    int sHeight = event.getResolution().getScaledHeight();
                    
                    // 自适应屏幕尺寸计算 - 修复自适应问题
                    float scaleFactor = Math.min(sWidth, sHeight) / 1080f; // 改为1080基准
                    scaleFactor = Math.max(0.6f, Math.min(1.0f, scaleFactor)); // 调整范围

                    // 面板尺寸和位置 - 稍微再调大一点点
                    int panelWidth = (int)(210 * scaleFactor);  // 从200增加到210
                    int panelHeight = (int)(85 * scaleFactor);  // 从80增加到85
                    int panelX = 10;
                    int panelY = sHeight - panelHeight - 10;

                    // 绘制黑色透明背景
                    GuiIngame.drawRect(panelX, panelY, panelX + panelWidth, panelY + panelHeight, 0x80000000);

                    // 头像区域 - 调整到面板垂直中间
                    int avatarSize = (int)(36 * scaleFactor);
                    int avatarX = panelX + (int)(6 * scaleFactor);
                    int avatarY = panelY + (panelHeight - avatarSize) / 2;  // 垂直居中

                    // 绘制玩家头像 - 优化版本
                    try {
                        // 绑定玩家皮肤纹理
                        mc.getTextureManager().bindTexture(mc.player.getLocationSkin());

                        // 设置渲染状态
                        GlStateManager.enableBlend();
                        GlStateManager.tryBlendFuncSeparate(
                            GlStateManager.SourceFactor.SRC_ALPHA,
                            GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA,
                            GlStateManager.SourceFactor.ONE,
                            GlStateManager.DestFactor.ZERO
                        );
                        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);

                        // 绘制头部基础层 - 8x8像素的头部区域
                        GuiIngame.drawScaledCustomSizeModalRect(
                            avatarX, avatarY,           // 屏幕位置
                            8.0F, 8.0F,                // UV坐标 - 头部在皮肤中的位置
                            8, 8,                      // 源区域大小 - 8x8像素的头部
                            avatarSize, avatarSize,    // 目标显示大小
                            64, 64                     // 皮肤纹理总尺寸
                        );

                        // 绘制头部外层/帽子层 - 覆盖在基础层上
                        GuiIngame.drawScaledCustomSizeModalRect(
                            avatarX, avatarY,           // 相同位置
                            40.0F, 8.0F,               // UV坐标 - 帽子层在皮肤中的位置
                            8, 8,                      // 源区域大小
                            avatarSize, avatarSize,    // 目标显示大小
                            64, 64                     // 皮肤纹理总尺寸
                        );

                        // 恢复渲染状态
                        GlStateManager.disableBlend();

                    } catch (Exception e) {
                        // 如果皮肤加载失败，显示默认头像
                        GlStateManager.color(0.55F, 0.27F, 0.07F, 1.0F); // 棕色
                        GuiIngame.drawRect(avatarX, avatarY, avatarX + avatarSize, avatarY + avatarSize, 0xFF8B4513);

                        // 添加简单的面部特征
                        GlStateManager.color(0.0F, 0.0F, 0.0F, 1.0F); // 黑色
                        int eyeSize = Math.max(2, avatarSize / 12);
                        int eyeY = avatarY + avatarSize / 3;

                        // 左眼
                        GuiIngame.drawRect(
                            avatarX + avatarSize / 4 - eyeSize/2, eyeY,
                            avatarX + avatarSize / 4 + eyeSize/2, eyeY + eyeSize,
                            0xFF000000
                        );

                        // 右眼
                        GuiIngame.drawRect(
                            avatarX + 3 * avatarSize / 4 - eyeSize/2, eyeY,
                            avatarX + 3 * avatarSize / 4 + eyeSize/2, eyeY + eyeSize,
                            0xFF000000
                        );

                        // 嘴巴
                        int mouthWidth = Math.max(4, avatarSize / 8);
                        int mouthHeight = Math.max(1, avatarSize / 24);
                        GuiIngame.drawRect(
                            avatarX + avatarSize / 2 - mouthWidth/2, avatarY + 2 * avatarSize / 3,
                            avatarX + avatarSize / 2 + mouthWidth/2, avatarY + 2 * avatarSize / 3 + mouthHeight,
                            0xFF000000
                        );

                        // 恢复颜色
                        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
                    }

                    // 状态信息区域 - 增加文字和条的间距
                    int infoX = avatarX + avatarSize + (int)(10 * scaleFactor);
                    int infoWidth = panelWidth - avatarSize - (int)(20 * scaleFactor);
                    int lineHeight = (int)(15 * scaleFactor);   // 从13增加到15，文字和条分开更多
                    int barHeight = (int)(6 * scaleFactor);
                    int spacing = (int)(8 * scaleFactor);

                    // 血量信息
                    float health = mc.player.getHealth();
                    float maxHealth = mc.player.getMaxHealth();
                    int healthTextY = panelY + (int)(8 * scaleFactor);

                    // 血量文字
                    String healthLabel = "血量: " + String.format("%d/%d", (int)health, (int)maxHealth);
                    mc.fontRenderer.drawStringWithShadow(healthLabel, infoX, healthTextY, 0xFFFFFFFF);

                    // 血量条 - 在文字下方留更多距离
                    int healthBarY = healthTextY + lineHeight;
                    GuiIngame.drawRect(infoX, healthBarY, infoX + infoWidth, healthBarY + barHeight, 0xFF202020);
                    float healthRatio = Math.min(health / maxHealth, 1.0f);
                    int healthWidth = (int)(healthRatio * infoWidth);
                    GuiIngame.drawRect(infoX, healthBarY, infoX + healthWidth, healthBarY + barHeight, 0xFFFF0000);

                    // 查克拉信息
                    Chakra.Pathway chakraPathway = Chakra.pathway(mc.player);
                    double chakra = chakraPathway.getAmount();
                    double maxChakra = chakraPathway.getMax();
                    int chakraTextY = healthBarY + barHeight + spacing;

                    // 查克拉文字
                    String chakraLabel = "查克拉: " + String.format("%.0f/%.0f", chakra, maxChakra);
                    mc.fontRenderer.drawStringWithShadow(chakraLabel, infoX, chakraTextY, 0xFF00FFFF);

                    // 查克拉条
                    int chakraBarY = chakraTextY + lineHeight;
                    GuiIngame.drawRect(infoX, chakraBarY, infoX + infoWidth, chakraBarY + barHeight, 0xFF333333);
                    if (maxChakra > 0) {
                        int chakraBarWidth = (int)(infoWidth * (chakra / maxChakra));
                        GuiIngame.drawRect(infoX, chakraBarY, infoX + chakraBarWidth, chakraBarY + barHeight, 0xFF00FFFF);
                    }

                    // 饥饿值信息
                    int hunger = mc.player.getFoodStats().getFoodLevel();
                    int maxHunger = 20;
                    int hungerTextY = chakraBarY + barHeight + spacing;

                    // 饥饿值文字
                    String hungerLabel = "饥饿值: " + String.format("%d/%d", hunger, maxHunger);
                    mc.fontRenderer.drawStringWithShadow(hungerLabel, infoX, hungerTextY, 0xFFFFAA00);

                    // 饥饿值条
                    int hungerBarY = hungerTextY + lineHeight;
                    GuiIngame.drawRect(infoX, hungerBarY, infoX + infoWidth, hungerBarY + barHeight, 0xFF333333);
                    if (maxHunger > 0) {
                        int hungerBarWidth = (int)(infoWidth * ((float)hunger / maxHunger));
                        GuiIngame.drawRect(infoX, hungerBarY, infoX + hungerBarWidth, hungerBarY + barHeight, 0xFFFFAA00);
                    }

                    // 疲劳值条 - 始终显示，无论是否戴眼睛
                    int fatigueBarWidth = (int)(4 * scaleFactor);
                    int fatigueBarHeight = panelHeight - (int)(12 * scaleFactor);
                    int fatigueBarX = panelX + panelWidth + (int)(2 * scaleFactor);
                    int fatigueBarY = panelY + (int)(6 * scaleFactor);

                    // 获取疲劳值
                    int fatigue = Pilaozhi.getFatigueLevel(mc.player);
                    int maxFatigue = Pilaozhi.getMaxFatigue();

                    // 绘制疲劳值条背景
                    GuiIngame.drawRect(fatigueBarX, fatigueBarY, fatigueBarX + fatigueBarWidth, fatigueBarY + fatigueBarHeight, 0xFF202020);

                    // 绘制疲劳值条 - 从底部向上填充
                    if (maxFatigue > 0) {
                        float fatigueRatio = (float)fatigue / maxFatigue;
                        int filledHeight = (int)(fatigueBarHeight * fatigueRatio);
                        int fillStartY = fatigueBarY + fatigueBarHeight - filledHeight;
                        
                        // 根据疲劳值选择颜色
                        int fatigueColor;
                        if (fatigueRatio > 0.6f) {
                            fatigueColor = 0xFF00FF00;  // 绿色 - 充足
                        } else if (fatigueRatio > 0.3f) {
                            fatigueColor = 0xFFFFFF00;  // 黄色 - 中等
                        } else {
                            fatigueColor = 0xFFFF0000;  // 红色 - 疲劳
                        }
                        
                        if (filledHeight > 0) {
                            GuiIngame.drawRect(fatigueBarX, fillStartY, fatigueBarX + fatigueBarWidth, fatigueBarY + fatigueBarHeight, fatigueColor);
                        }
                    }
                }
            }
        }
    }
}