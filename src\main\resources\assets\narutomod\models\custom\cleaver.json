{"credit": "Made with Blockbench", "texture_size": [64, 64], "elements": [{"from": [7.7, -8, 7.25], "to": [8.3, 0, 8.25], "faces": {"north": {"uv": [8.5, 1, 9.5, 8], "texture": "#0"}, "east": {"uv": [7.5, 8, 8.5, 0], "texture": "#0"}, "south": {"uv": [8.5, 1, 9.5, 8], "texture": "#0"}, "west": {"uv": [7.5, 8, 8.5, 0], "texture": "#0"}, "up": {"uv": [7, 1.75, 6.75, 1.5], "texture": "#0"}, "down": {"uv": [8.5, 0, 9.5, 1], "texture": "#0"}}}, {"from": [7.3, 0, 7], "to": [8.7, 24, 10], "faces": {"north": {"uv": [1.5, 0, 1.875, 6], "texture": "#0"}, "east": {"uv": [0, 0, 0.75, 6], "texture": "#0"}, "south": {"uv": [2, 0, 2.375, 6], "texture": "#0"}, "west": {"uv": [0.75, 0, 0, 6], "texture": "#0"}, "up": {"uv": [7.125, 0.75, 6.75, 0], "texture": "#0"}, "down": {"uv": [7.125, 0.75, 6.75, 1.5], "texture": "#0"}}}, {"from": [7.35, 0, 6.5], "to": [8.65, 24, 7], "faces": {"north": {"uv": [2.5, 0, 2.8125, 6], "texture": "#0"}, "east": {"uv": [0.25, 6, 0.375, 12], "texture": "#0"}, "west": {"uv": [0.375, 6, 0.25, 12], "texture": "#0"}, "up": {"uv": [7.0625, 2.125, 6.75, 2], "texture": "#0"}, "down": {"uv": [7.0625, 2.25, 6.75, 2.375], "texture": "#0"}}}, {"from": [7.4, 0, 6], "to": [8.6, 24, 6.5], "faces": {"north": {"uv": [3.5, 0, 3.8125, 6], "texture": "#0"}, "east": {"uv": [0.75, 6, 0.875, 12], "texture": "#0"}, "west": {"uv": [0.875, 6, 0.75, 12], "texture": "#0"}, "up": {"uv": [7.0625, 2.625, 6.75, 2.5], "texture": "#0"}, "down": {"uv": [7.0625, 2.75, 6.75, 2.875], "texture": "#0"}}}, {"from": [7.45, 0, 5.5], "to": [8.55, 24, 6], "faces": {"north": {"uv": [4.5, 0, 4.75, 6], "texture": "#0"}, "east": {"uv": [1.25, 6, 1.375, 12], "texture": "#0"}, "west": {"uv": [1.375, 6, 1.25, 12], "texture": "#0"}, "up": {"uv": [7, 3.125, 6.75, 3], "texture": "#0"}, "down": {"uv": [7, 3.25, 6.75, 3.375], "texture": "#0"}}}, {"from": [7.5, 0, 5], "to": [8.5, 24, 5.5], "faces": {"north": {"uv": [5, 0, 5.25, 6], "texture": "#0"}, "east": {"uv": [1.75, 6, 1.875, 12], "texture": "#0"}, "west": {"uv": [1.875, 6, 1.75, 12], "texture": "#0"}, "up": {"uv": [7, 3.625, 6.75, 3.5], "texture": "#0"}, "down": {"uv": [7, 3.75, 6.75, 3.875], "texture": "#0"}}}, {"from": [7.55, 0, 4.5], "to": [8.45, 24, 5], "faces": {"north": {"uv": [5.5, 0, 5.75, 6], "texture": "#0"}, "east": {"uv": [2.25, 6, 2.375, 12], "texture": "#0"}, "west": {"uv": [2.375, 6, 2.25, 12], "texture": "#0"}, "up": {"uv": [7, 4.125, 6.75, 4], "texture": "#0"}, "down": {"uv": [7, 4.25, 6.75, 4.375], "texture": "#0"}}}, {"from": [7.6, 0, 4], "to": [8.4, 24, 4.5], "faces": {"north": {"uv": [0, 6, 0.1875, 12], "texture": "#0"}, "east": {"uv": [2.75, 6, 2.875, 12], "texture": "#0"}, "west": {"uv": [2.875, 6, 2.75, 12], "texture": "#0"}, "up": {"uv": [6.9375, 4.625, 6.75, 4.5], "texture": "#0"}, "down": {"uv": [6.9375, 4.75, 6.75, 4.875], "texture": "#0"}}}, {"from": [7.7, 0, 3.5], "to": [8.3, 24, 4], "faces": {"north": {"uv": [3.25, 6, 3.375, 12], "texture": "#0"}, "east": {"uv": [3.5, 6, 3.625, 12], "texture": "#0"}, "west": {"uv": [3.625, 6, 3.5, 12], "texture": "#0"}, "up": {"uv": [6.875, 5.125, 6.75, 5], "texture": "#0"}, "down": {"uv": [6.875, 5.25, 6.75, 5.375], "texture": "#0"}}}, {"from": [7.8, 0, 3], "to": [8.2, 24, 3.5], "faces": {"north": {"uv": [4.25, 6, 4.375, 12], "texture": "#0"}, "east": {"uv": [4.5, 6, 4.625, 12], "texture": "#0"}, "west": {"uv": [4.625, 6, 4.5, 12], "texture": "#0"}, "up": {"uv": [6.875, 5.625, 6.75, 5.5], "texture": "#0"}, "down": {"uv": [6.875, 5.75, 6.75, 5.875], "texture": "#0"}}}, {"from": [7.9, 0, 2.5], "to": [8.1, 24, 3], "faces": {"north": {"uv": [6.5, 0, 6.5625, 6], "texture": "#0"}, "east": {"uv": [5.25, 6, 5.375, 12], "texture": "#0"}, "west": {"uv": [5.375, 6, 5.25, 12], "texture": "#0"}, "up": {"uv": [6.8125, 6.125, 6.75, 6], "texture": "#0"}, "down": {"uv": [6.8125, 6.25, 6.75, 6.375], "texture": "#0"}}}, {"from": [8, 0, 2], "to": [8, 24, 2.5], "faces": {"east": {"uv": [5.75, 6, 5.875, 12], "texture": "#0"}, "west": {"uv": [5.875, 6, 5.75, 12], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"rotation": [-10, 0, 0], "translation": [0, 8.5, 0]}, "thirdperson_lefthand": {"rotation": [-10, 0, 0], "translation": [0, 8.5, 0]}, "firstperson_righthand": {"rotation": [-10, 0, 0], "translation": [0, 8.5, 0]}, "firstperson_lefthand": {"rotation": [-10, 0, 0], "translation": [0, 8.5, 0]}, "ground": {"rotation": [0, 0, 180], "translation": [0, 10, 0]}, "gui": {"rotation": [-60, -40, -60], "translation": [-1, -0.25, -0.5], "scale": [0.7, 0.7, 0.7]}, "head": {"rotation": [90, -70, -90], "translation": [-5, -18, 5], "scale": [1.5, 1.5, 1.5]}, "fixed": {"rotation": [-112, 45, 105], "translation": [0.5, 0, -0.5], "scale": [0.8, 0.8, 0.8]}}, "groups": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [0, {"name": "group", "origin": [0, 0, 0], "color": 0, "children": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}]}]}