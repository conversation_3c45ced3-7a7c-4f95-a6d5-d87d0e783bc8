package net.narutomod.util;

import net.minecraft.world.Teleporter;
import net.minecraft.world.WorldServer;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.math.BlockPos;
import net.minecraftforge.common.util.ITeleporter;

import java.util.Optional;

public class CustomTeleporter extends Teleporter {
	
	private final WorldServer world;
	private double x;
	private double y;
	private double z;
	
	public CustomTeleporter(WorldServer world, double x, double y, double z) {
		super(world);
		this.world = world;
		this.x = x;
		this.y = y;
		this.z = z;
	}
	
	@Override
	public void placeInPortal(Entity entity, float rotationYaw) {
		// 确保目标位置的区块已加载
		this.world.getChunkFromBlockCoords(new BlockPos((int)this.x, (int)this.y, (int)this.z));
		
		// 设置实体位置
		entity.setLocationAndAngles(this.x, this.y, this.z, entity.rotationYaw, entity.rotationPitch);
		
		// 清除实体速度
		entity.motionX = 0.0D;
		entity.motionY = 0.0D;
		entity.motionZ = 0.0D;
	}
	
	/**
	 * 传送实体到指定维度
	 * @param entity 要传送的实体
	 * @param dimension 目标维度ID
	 * @param x 目标X坐标
	 * @param y 目标Y坐标
	 * @param z 目标Z坐标
	 * @return 传送后的实体（可能为null）
	 */
	public static Optional<Entity> teleportToDimension(EntityLivingBase entity, int dimension, double x, double y, double z) {
		if (entity.world.isRemote) {
			return Optional.empty();
		}
		
		int oldDimension = entity.world.provider.getDimension();
		MinecraftServer server = entity.world.getMinecraftServer();
		
		if (server == null) {
			return Optional.empty();
		}
		
		WorldServer worldServer = server.getWorld(dimension);
		if (worldServer == null) {
			throw new IllegalArgumentException("Dimension: " + dimension + " doesn't exist!");
		}
		
		// 如果是玩家，设置传送冷却时间
		if (entity instanceof EntityPlayer) {
			((EntityPlayer) entity).timeUntilPortal = 0;
		}
		
		// 执行维度传送
		Entity newEntity = entity.changeDimension(dimension, new CustomTeleporter(worldServer, x, y, z));
		
		if (newEntity == null) {
			return Optional.empty();
		}
		
		// 确保位置正确设置
		newEntity.setLocationAndAngles(x, y, z, newEntity.rotationYaw, newEntity.rotationPitch);
		
		return Optional.of(newEntity);
	}
	
	/**
	 * 传送实体回到原始维度
	 * @param entity 要传送的实体
	 * @param coords 原始坐标字符串 "dimension,x,y,z"
	 * @return 传送后的实体（可能为null）
	 */
	public static Optional<Entity> teleportBack(EntityLivingBase entity, String coords) {
		if (coords == null || coords.isEmpty()) {
			return Optional.empty();
		}
		
		try {
			String[] coordsArray = coords.split(",");
			if (coordsArray.length != 4) {
				return Optional.empty();
			}
			
			int dimension = Integer.parseInt(coordsArray[0]);
			double x = Double.parseDouble(coordsArray[1]);
			double y = Double.parseDouble(coordsArray[2]);
			double z = Double.parseDouble(coordsArray[3]);
			
			return teleportToDimension(entity, dimension, x, y, z);
		} catch (Exception e) {
			e.printStackTrace();
			return Optional.empty();
		}
	}
}
