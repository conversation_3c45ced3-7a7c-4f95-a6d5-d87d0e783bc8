package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.Chakra;
import net.narutomod.PlayerTracker;
import net.narutomod.potion.PotionParalysis;

import net.minecraft.world.World;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextComponentTranslation;
import net.minecraft.util.text.ITextComponent;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.ResourceLocation;
import net.minecraft.potion.PotionEffect;
import net.minecraft.init.MobEffects;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.item.ItemStack;

import java.util.Map;
import java.util.HashMap;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureT<PERSON><PERSON><PERSON><PERSON> extends ElementsNarutomodMod.ModElement {

	// 月读状态追踪
	public static Map<Integer, EntityLivingBase> tsukuyomiList = new HashMap<>();

	// 月读技能配置
	private static final double CHAKRA_COST = 2000.0D;
	private static final double MIN_CHAKRA_REQUIRED = 8000.0D;
	private static final int DURATION = 600; // 30秒
	private static final int COOLDOWN = 6000; // 5分钟冷却
	private static final double MIN_BATTLE_XP = 2000.0D;
	private static final String COOLDOWN_KEY = "tsukuyomi_cd";

	public ProcedureTsukuyomi(ElementsNarutomodMod instance) {
		super(instance, 601);
	}

	// 基于源项目的execute方法
	public static void execute(EntityLivingBase entity) {
		if (entity == null || entity.world == null) {
			return;
		}

		World world = entity.world;

		// 检查冷却时间
		if (entity instanceof EntityPlayer) {
			EntityPlayer player = (EntityPlayer) entity;
			double cooldown = player.getEntityData().getDouble(COOLDOWN_KEY);
			if (world.getTotalWorldTime() < cooldown) {
				long remainingTime = (long)((cooldown - world.getTotalWorldTime()) / 20);
				player.sendStatusMessage(new net.minecraft.util.text.TextComponentTranslation("chattext.cooldown.formatted", remainingTime), true);
				return;
			}
		}

		// 检查是否已经激活月读
		boolean isActive = isJutsuActive(entity, "tsukuyomi");

		if (!isActive) {
			// 激活月读
			if (entity instanceof EntityPlayer) {
				ItemStack helmet = ((EntityPlayer) entity).inventory.armorInventory.get(3);
				if (!helmet.isEmpty() && helmet.hasTagCompound()) {
					net.minecraft.nbt.NBTTagCompound tag = helmet.getTagCompound();
					if (tag != null && tag.getBoolean("sharingan_blinded")) {
						return;
					}
				}

				if (PlayerTracker.getBattleXp((EntityPlayer) entity) < MIN_BATTLE_XP) {
					return;
				}
			}

			// 寻找目标
			RayTraceResult trace = ProcedureUtils.objectEntityLookingAt(entity, 30.0D);
			EntityLivingBase target = null;

			if (trace.typeOfHit == RayTraceResult.Type.ENTITY && trace.entityHit instanceof EntityLivingBase) {
				target = (EntityLivingBase) trace.entityHit;
			}

			if (target != null && Chakra.pathway(entity).consume(CHAKRA_COST)) {
				// 激活月读效果
				activateJutsu(target, "tsukuyomi_buff", DURATION);

				if (entity instanceof EntityPlayer) {
					activateJutsu(entity, "tsukuyomi", DURATION);
					sendSafeMessage(entity, TextFormatting.DARK_PURPLE + "月读发动！");
					// 设置冷却时间
					((EntityPlayer) entity).getEntityData().setDouble(COOLDOWN_KEY, world.getTotalWorldTime() + COOLDOWN);
				} else {
					activateJutsu(entity, "tsukuyomi", DURATION);
				}

				tsukuyomiList.put(entity.getEntityId(), target);

				// 播放幻术音效
				net.minecraft.util.SoundEvent soundEvent = net.minecraft.util.SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:genjutsu"));
				if (soundEvent != null) {
					world.playSound(null, entity.posX, entity.posY, entity.posZ, soundEvent, SoundCategory.NEUTRAL, 1.0F, 1.0F);
				}
			}
		} else {
			// 主动解除月读 (服务器兼容)
			if (world.isRemote) {
				return; // 只在服务器端执行
			}

			EntityLivingBase target = tsukuyomiList.get(entity.getEntityId());
			if (target != null && !target.isDead) {
				// 清除目标的月读效果
				clearTsukuyomi(target);

				// 安全移除追踪
				try {
					tsukuyomiList.remove(entity.getEntityId());
				} catch (Exception e) {
					// 静默处理并发异常
				}

				// 清除施术者的月读状态 (只有在成功解除时才清除)
				deactivateJutsu(entity, "tsukuyomi");

				sendSafeMessage(entity, TextFormatting.GREEN + "月读解除！");
			} else {
				// 如果没有激活的目标，不清除施术者状态，只显示信息
				if (entity instanceof EntityPlayer) {
					EntityPlayer player = (EntityPlayer) entity;
					double cooldown = player.getEntityData().getDouble(COOLDOWN_KEY);
					if (world.getTotalWorldTime() < cooldown) {
						long remainingTime = (long)((cooldown - world.getTotalWorldTime()) / 20);
						player.sendStatusMessage(new net.minecraft.util.text.TextComponentTranslation("chattext.cooldown.formatted", remainingTime), true);
					} else {
						sendSafeMessage(player, TextFormatting.YELLOW + "没有激活的月读可以解除");
					}
				}
				// 注意：这里不清除施术者状态，保持月读激活状态
			}
		}
	}

	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure ProcedureTsukuyomi!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure ProcedureTsukuyomi!");
			return;
		}
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure ProcedureTsukuyomi!");
			return;
		}

		Entity entity = (Entity) dependencies.get("entity");
		World world = (World) dependencies.get("world");
		boolean is_pressed = (Boolean) dependencies.get("is_pressed");
		boolean is_sneaking = dependencies.get("is_sneaking") != null ? (Boolean) dependencies.get("is_sneaking") : false;

		if (!(entity instanceof EntityLivingBase)) {
			System.err.println("Unauthorized calling of procedure ProcedureTsukuyomi!");
			return;
		}

		// 只在按下时执行
		if (!is_pressed) {
			return;
		}

		// 检查是否按下Shift键 - Shift+忍术3用于强制解除月读
		if (is_sneaking) {
			// 防止重复触发 - 检查是否刚刚使用过强制解除
			if (entity instanceof EntityPlayer) {
				EntityPlayer player = (EntityPlayer) entity;
				long lastForceUse = player.getEntityData().getLong("tsukuyomi_force_last_use");
				if (world.getTotalWorldTime() - lastForceUse < 10) { // 0.5秒内防止重复
					return;
				}
				player.getEntityData().setLong("tsukuyomi_force_last_use", world.getTotalWorldTime());
			}

			forceDeactivateTsukuyomi((EntityLivingBase) entity, world);
			return;
		}

		if (entity instanceof EntityPlayer &&
			Chakra.pathway((EntityLivingBase) entity).getMax() < MIN_CHAKRA_REQUIRED) {
			sendSafeMessage((EntityLivingBase) entity, TextFormatting.RED +
				"需要至少 " + (int)MIN_CHAKRA_REQUIRED + " 查克拉上限才能使用月读！");
			return;
		}

		execute((EntityLivingBase) entity);
	}

	// 简化的忍术管理方法
	private static boolean isJutsuActive(EntityLivingBase entity, String jutsuName) {
		return entity.getEntityData().getBoolean(jutsuName + "_active");
	}

	private static void activateJutsu(EntityLivingBase entity, String jutsuName, int duration) {
		entity.getEntityData().setBoolean(jutsuName + "_active", true);
		entity.getEntityData().setInteger(jutsuName + "_duration", duration);

		// 应用具体效果
		if (jutsuName.equals("tsukuyomi_buff")) {
			// 对目标施加月读效果
			entity.addPotionEffect(new PotionEffect(PotionParalysis.potion, duration, 1, false, false));
			entity.addPotionEffect(new PotionEffect(MobEffects.BLINDNESS, duration + 40, 0, false, true));
			entity.addPotionEffect(new PotionEffect(MobEffects.NAUSEA, duration, 0, false, true));
			entity.addPotionEffect(new PotionEffect(MobEffects.SLOWNESS, duration, 4, false, false));
			entity.addPotionEffect(new PotionEffect(MobEffects.WEAKNESS, duration, 2, false, false));

			sendSafeMessage(entity, TextFormatting.DARK_RED + "你被月读困住了！");
		}
	}

	private static void deactivateJutsu(EntityLivingBase entity, String jutsuName) {
		if (entity == null || entity.world == null || entity.isDead) {
			return;
		}

		// 只在服务器端执行数据修改
		if (!entity.world.isRemote) {
			entity.getEntityData().setBoolean(jutsuName + "_active", false);
			entity.getEntityData().removeTag(jutsuName + "_duration");
		}

		// 移除具体效果
		if (jutsuName.equals("tsukuyomi_buff")) {
			// 安全移除药水效果
			try {
				if (PotionParalysis.potion != null) {
					entity.removePotionEffect(PotionParalysis.potion);
				}
				entity.removePotionEffect(MobEffects.BLINDNESS);
				entity.removePotionEffect(MobEffects.NAUSEA);
				entity.removePotionEffect(MobEffects.SLOWNESS);
				entity.removePotionEffect(MobEffects.WEAKNESS);
			} catch (Exception e) {
				// 静默处理药水移除异常
			}

			// 使用安全的消息发送方法
			sendSafeMessage(entity, TextFormatting.GREEN + "月读效果已解除！");
		}
	}

	// 清除月读效果 (服务器兼容) - 只清除目标效果，不处理追踪列表
	public static void clearTsukuyomi(EntityLivingBase target) {
		if (target == null || target.world == null) {
			return;
		}

		// 只在服务器端执行
		if (target.world.isRemote) {
			return;
		}

		deactivateJutsu(target, "tsukuyomi_buff");
		// 注意：不清除目标的"tsukuyomi"状态，因为目标不是施术者

		// 注意：不在这里处理追踪列表，由调用者处理
	}

	// 检查并清理过期的月读效果
	public static void checkTsukuyomiExpiry(EntityLivingBase entity, World world) {
		// 检查月读施术者
		if (entity.getEntityData().getBoolean("tsukuyomi_active")) {
			int duration = entity.getEntityData().getInteger("tsukuyomi_duration");
			if (duration > 0) {
				entity.getEntityData().setInteger("tsukuyomi_duration", duration - 1);
			} else {
				deactivateJutsu(entity, "tsukuyomi");
				if (tsukuyomiList.get(entity.getEntityId()) != null) {
					deactivateJutsu(tsukuyomiList.get(entity.getEntityId()), "tsukuyomi_buff");
				}
				tsukuyomiList.remove(entity.getEntityId());
			}
		}

		// 检查月读目标
		if (entity.getEntityData().getBoolean("tsukuyomi_buff_active")) {
			int duration = entity.getEntityData().getInteger("tsukuyomi_buff_duration");
			if (duration > 0) {
				entity.getEntityData().setInteger("tsukuyomi_buff_duration", duration - 1);
			} else {
				deactivateJutsu(entity, "tsukuyomi_buff");
			}
		}
	}

	// 强制解除月读 - Shift+忍术3专用，不检查冷却
	private static void forceDeactivateTsukuyomi(EntityLivingBase entity, World world) {
		if (entity == null || world == null) {
			return;
		}

		// 只在服务器端执行
		if (world.isRemote) {
			return;
		}

		// 优先检查并解除自身的幻术状态
		boolean selfCleared = clearSelfGenjutsuEffects(entity);

		if (selfCleared) {
			// 如果解除了自身状态，就不再处理目标
			return;
		}

		// 如果自身没有幻术状态，则尝试解除目标的幻术
		clearTargetGenjutsuEffects(entity, world);
	}

	// 安全的消息发送方法，避免玩家退出时的空指针异常
	private static void sendSafeMessage(EntityLivingBase entity, String message) {
		if (!(entity instanceof EntityPlayer) || entity.world.isRemote) {
			return;
		}

		try {
			EntityPlayer player = (EntityPlayer) entity;
			// 检查玩家连接状态，避免在退出时发送消息
			if (player instanceof EntityPlayerMP) {
				EntityPlayerMP playerMP = (EntityPlayerMP) player;
				// 检查网络连接是否仍然有效
				if (playerMP.connection != null && playerMP.connection.netManager != null &&
					playerMP.connection.netManager.isChannelOpen()) {
					player.sendMessage(new TextComponentString(message));
				}
			} else {
				// 单人模式下直接发送
				player.sendMessage(new TextComponentString(message));
			}
		} catch (Exception e) {
			// 静默处理消息发送异常，避免崩溃
			System.err.println("Failed to send message to player: " + e.getMessage());
		}
	}

	// 检查并解除自身的幻术效果（月读、忍术幻术、基础幻术）
	private static boolean clearSelfGenjutsuEffects(EntityLivingBase entity) {
		boolean cleared = false;

		// 检查月读效果
		if (entity.getEntityData().getBoolean("tsukuyomi_buff_active")) {
			deactivateJutsu(entity, "tsukuyomi_buff");
			cleared = true;
			sendSafeMessage(entity, TextFormatting.GREEN + "月读效果已解除！");
		}

		// 检查忍术幻术效果
		if (entity.getEntityData().getBoolean("genjutsu_active")) {
			net.narutomod.procedure.ProcedureGenjutsu.clearGenjutsu(entity);
			cleared = true;
			sendSafeMessage(entity, TextFormatting.GREEN + "忍术幻术效果已解除！");
		}

		// 检查基础幻术效果
		if (entity.getEntityData().getBoolean("genjutsu_basic_active")) {
			net.narutomod.procedure.ProcedureGenjutsuBasic.clearGenjutsu(entity);
			cleared = true;
			sendSafeMessage(entity, TextFormatting.GREEN + "基础幻术效果已解除！");
		}

		return cleared;
	}

	// 解除目标的幻术效果
	private static void clearTargetGenjutsuEffects(EntityLivingBase entity, World world) {
		boolean targetCleared = false;

		// 查找当前激活的月读目标
		EntityLivingBase tsukuyomiTarget = tsukuyomiList.get(entity.getEntityId());
		if (tsukuyomiTarget != null && !tsukuyomiTarget.isDead) {
			// 清除目标的月读效果
			clearTsukuyomi(tsukuyomiTarget);

			// 安全移除追踪
			try {
				tsukuyomiList.remove(entity.getEntityId());
			} catch (Exception e) {
				// 静默处理并发异常
			}

			// 清除施术者的月读状态
			deactivateJutsu(entity, "tsukuyomi");
			targetCleared = true;
		}

		// 查找并解除忍术幻术目标
		if (net.narutomod.procedure.ProcedureGenjutsu.clearCasterGenjutsu(entity, world)) {
			targetCleared = true;
		}

		// 查找并解除基础幻术目标
		EntityLivingBase basicGenjutsuTarget = net.narutomod.procedure.ProcedureGenjutsuBasic.getCasterTarget(entity);
		if (basicGenjutsuTarget != null && !basicGenjutsuTarget.isDead) {
			net.narutomod.procedure.ProcedureGenjutsuBasic.clearGenjutsu(basicGenjutsuTarget);
			net.narutomod.procedure.ProcedureGenjutsuBasic.removeCasterTarget(entity);
			targetCleared = true;
		}

		// 发送结果消息
		if (targetCleared) {
			sendSafeMessage(entity, TextFormatting.GREEN + "目标幻术效果已解除！");
		} else {
			sendSafeMessage(entity, TextFormatting.YELLOW + "没有找到可解除的目标幻术");
		}
	}
}
