{"credit": "Made with Blockbench", "ambientocclusion": false, "elements": [{"from": [7.575, 7.65, 7], "to": [8.425, 8.5, 9], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8.075, 8]}, "faces": {"north": {"uv": [0, 6, 1, 7], "texture": "#0"}, "east": {"uv": [0, 0, 2, 1], "texture": "#0"}, "south": {"uv": [8, 1, 9, 2], "texture": "#0"}, "west": {"uv": [0, 1, 2, 2], "texture": "#0"}, "up": {"uv": [1, 4, 0, 2], "texture": "#0"}, "down": {"uv": [3, 0, 2, 2], "texture": "#0"}}}, {"from": [7.575, 7.65, 7], "to": [8.425, 8.5, 9], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8.075, 8]}, "faces": {"north": {"uv": [8, 3, 9, 4], "texture": "#0"}, "east": {"uv": [1, 2, 3, 3], "texture": "#0"}, "south": {"uv": [8, 4, 9, 5], "texture": "#0"}, "west": {"uv": [3, 0, 5, 1], "texture": "#0"}, "up": {"uv": [2, 5, 1, 3], "texture": "#0"}, "down": {"uv": [4, 1, 3, 3], "texture": "#0"}}}, {"from": [7.575, 7.075, 7.575], "to": [8.425, 9.075, 8.425], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8.075, 8]}, "faces": {"north": {"uv": [2, 3, 3, 5], "texture": "#0"}, "east": {"uv": [3, 3, 4, 5], "texture": "#0"}, "south": {"uv": [0, 4, 1, 6], "texture": "#0"}, "west": {"uv": [4, 1, 5, 3], "texture": "#0"}, "up": {"uv": [9, 6, 8, 5], "texture": "#0"}, "down": {"uv": [7, 8, 6, 9], "texture": "#0"}}}, {"from": [7.575, 7.075, 7.575], "to": [8.425, 9.075, 8.425], "rotation": {"angle": 45, "axis": "x", "origin": [8, 8.075, 8]}, "faces": {"north": {"uv": [4, 3, 5, 5], "texture": "#0"}, "east": {"uv": [5, 0, 6, 2], "texture": "#0"}, "south": {"uv": [1, 5, 2, 7], "texture": "#0"}, "west": {"uv": [2, 5, 3, 7], "texture": "#0"}, "up": {"uv": [9, 7, 8, 6], "texture": "#0"}, "down": {"uv": [8, 8, 7, 9], "texture": "#0"}}}, {"from": [7.575, 7.65, 7], "to": [8.425, 8.5, 9], "rotation": {"angle": 45, "axis": "y", "origin": [8, 8.15, 8]}, "faces": {"north": {"uv": [8, 7, 9, 8], "texture": "#0"}, "east": {"uv": [5, 2, 7, 3], "texture": "#0"}, "south": {"uv": [8, 8, 9, 9], "texture": "#0"}, "west": {"uv": [3, 5, 5, 6], "texture": "#0"}, "up": {"uv": [6, 5, 5, 3], "texture": "#0"}, "down": {"uv": [6, 5, 5, 7], "texture": "#0"}}}, {"from": [7, 7.65, 7.575], "to": [9, 8.5, 8.425], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8.075, 8]}, "faces": {"north": {"uv": [6, 0, 8, 1], "texture": "#0"}, "east": {"uv": [0, 9, 1, 10], "texture": "#0"}, "south": {"uv": [6, 1, 8, 2], "texture": "#0"}, "west": {"uv": [1, 9, 2, 10], "texture": "#0"}, "up": {"uv": [5, 7, 3, 6], "texture": "#0"}, "down": {"uv": [8, 3, 6, 4], "texture": "#0"}}}, {"from": [7, 7.65, 7.575], "to": [9, 8.5, 8.425], "rotation": {"angle": 45, "axis": "z", "origin": [8, 8.075, 8]}, "faces": {"north": {"uv": [6, 4, 8, 5], "texture": "#0"}, "east": {"uv": [9, 1, 10, 2], "texture": "#0"}, "south": {"uv": [6, 5, 8, 6], "texture": "#0"}, "west": {"uv": [2, 9, 3, 10], "texture": "#0"}, "up": {"uv": [8, 7, 6, 6], "texture": "#0"}, "down": {"uv": [2, 7, 0, 8], "texture": "#0"}}}, {"from": [7.575, 7.075, 7.575], "to": [8.425, 9.075, 8.425], "rotation": {"angle": 45, "axis": "z", "origin": [8, 8.075, 8]}, "faces": {"north": {"uv": [2, 7, 3, 9], "texture": "#0"}, "east": {"uv": [3, 7, 4, 9], "texture": "#0"}, "south": {"uv": [4, 7, 5, 9], "texture": "#0"}, "west": {"uv": [5, 7, 6, 9], "texture": "#0"}, "up": {"uv": [10, 3, 9, 2], "texture": "#0"}, "down": {"uv": [4, 9, 3, 10], "texture": "#0"}}}, {"from": [7, 7.65, 7.575], "to": [9, 8.5, 8.425], "rotation": {"angle": 45, "axis": "y", "origin": [8, 8.15, 8]}, "faces": {"north": {"uv": [7, 2, 9, 3], "texture": "#0"}, "east": {"uv": [9, 3, 10, 4], "texture": "#0"}, "south": {"uv": [6, 7, 8, 8], "texture": "#0"}, "west": {"uv": [4, 9, 5, 10], "texture": "#0"}, "up": {"uv": [2, 9, 0, 8], "texture": "#0"}, "down": {"uv": [10, 0, 8, 1], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"rotation": [45, 45, 45], "scale": [1.5, 1.5, 1.5]}, "thirdperson_lefthand": {"rotation": [45, 45, 45], "scale": [1.5, 1.5, 1.5]}, "firstperson_righthand": {"rotation": [45, 45, 45], "scale": [1.5, 1.5, 1.5]}, "firstperson_lefthand": {"rotation": [45, 45, 45], "scale": [1.5, 1.5, 1.5]}, "ground": {"rotation": [45, 45, 45], "scale": [1.5, 1.5, 1.5]}, "gui": {"rotation": [45, 45, 45], "scale": [4, 4, 4]}, "fixed": {"rotation": [45, 45, 45], "translation": [0, 0, -2], "scale": [2, 2, 2]}}, "groups": [{"name": "group", "origin": [0, 0, 0], "color": 0, "children": [0, 1, 2, 3, 4, 5, 6, 7, 8]}]}