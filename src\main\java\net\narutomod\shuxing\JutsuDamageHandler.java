package net.narutomod.shuxing;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.DamageSource;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.common.MinecraftForge;
import net.narutomod.item.ItemJutsu;

public class JutsuDamageHandler {

    @SubscribeEvent
    public void onLivingHurt(LivingHurtEvent event) {
        DamageSource source = event.getSource();

        // 检查是否是忍术伤害
        if (source.getTrueSource() instanceof EntityPlayer) {
            EntityPlayer attacker = (EntityPlayer) source.getTrueSource();

            // 根据伤害源类型应用对应的属性加成
            String elementType = getElementTypeFromDamage(source);
            if (elementType != null) {
                float originalDamage = event.getAmount();
                float bonusDamage = AttributeManager.applyElementalDamageBonus(attacker, originalDamage, elementType);
                event.setAmount(bonusDamage);
            }
        }
    }

    private String getElementTypeFromDamage(DamageSource source) {
        String damageType = source.getDamageType();

        // 检查是否是忍术伤害
        if (ItemJutsu.isDamageSourceJutsu(source)) {
            // 根据忍术类型返回对应元素
            if (ItemJutsu.isDamageSourceNinjutsu(source)) {
                return "ninjutsu";
            }
            if (ItemJutsu.isDamageSourceSenjutsu(source)) {
                return "senjutsu";
            }
        }

        // 根据伤害类型名称判断
        if (damageType.contains("katon") || damageType.contains("fire")) return "katon";
        if (damageType.contains("futon") || damageType.contains("wind")) return "futon";
        if (damageType.contains("suiton") || damageType.contains("water")) return "suiton";
        if (damageType.contains("raiton") || damageType.contains("lightning")) return "raiton";
        if (damageType.contains("doton") || damageType.contains("earth")) return "doton";

        return null;
    }
}