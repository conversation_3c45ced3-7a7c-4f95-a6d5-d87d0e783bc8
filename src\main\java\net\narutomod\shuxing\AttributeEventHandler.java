package net.narutomod.shuxing;

import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.eventhandler.EventPriority;
import net.minecraftforge.fml.common.gameevent.PlayerEvent;
import net.minecraftforge.event.entity.living.LivingDeathEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraftforge.fml.common.gameevent.TickEvent;

public class AttributeEventHandler {
    
    @SubscribeEvent
    public void onPlayerLoggedIn(PlayerEvent.PlayerLoggedInEvent event) {
        if (event.player instanceof EntityPlayerMP) {
            EntityPlayerMP player = (EntityPlayerMP) event.player;
            
            // 延迟同步，确保客户端准备就绪
            player.getServerWorld().addScheduledTask(() -> {
                AttributeEnhancement attr = AttributeEnhancement.get(player);
                if (attr != null) {
                    // 玩家登录时同步属性数据
                    attr.syncToClient(player);
                    
                    // 同时更新查克拉上限
                    net.narutomod.shuxing.AttributeManager.updatePlayerChakraLimit(player);
                }
            });
        }
    }

    @SubscribeEvent
    public void onPlayerChangedDimension(PlayerEvent.PlayerChangedDimensionEvent event) {
        if (event.player instanceof EntityPlayerMP) {
            EntityPlayerMP player = (EntityPlayerMP) event.player;
            
            // 跨维度时重新同步
            player.getServerWorld().addScheduledTask(() -> {
                AttributeEnhancement attr = AttributeEnhancement.get(player);
                if (attr != null) {
                    attr.syncToClient(player);
                }
            });
        }
    }

    @SubscribeEvent
    public void onPlayerDeath(LivingDeathEvent event) {
        // 玩家死亡时设置标记，用于复活后恢复查克拉
        if (event.getEntityLiving() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntityLiving();

            if (!player.world.isRemote) {
                // 设置死亡标记，用于区分死亡复活和维度传送
                player.getEntityData().setBoolean("JustDied", true);
                System.out.println("玩家死亡: " + player.getName() + " - 设置死亡标记");
            }
        }
    }

    @SubscribeEvent
    public void onPlayerTick(TickEvent.PlayerTickEvent event) {
        if (event.phase == TickEvent.Phase.END && !event.player.world.isRemote) {
            EntityPlayer player = event.player;

            // 检查是否刚重生且需要恢复查克拉（只有死亡复活才会触发）
            if (player.getEntityData().getBoolean("NeedChakraRestore")) {
                AttributeEnhancement attr = AttributeEnhancement.get(player);

                if (attr != null) { // 删除 && attr.getAttributeLevel(6) > 0 条件
                    net.narutomod.Chakra.Pathway pathway = net.narutomod.Chakra.pathway(player);

                    if (pathway != null && pathway.getMax() > 0) {
                        double maxChakra = pathway.getMax();
                        double retentionRatio = attr.getDeathChakraBonus();
                        double restoredChakra = maxChakra * retentionRatio;

                        pathway.consume(-restoredChakra, true);

                        if (player instanceof EntityPlayerMP) {
                            String message = String.format("§6轮回转生生效: 恢复了 %.1f%% 查克拉 (%.0f)",
                                retentionRatio * 100.0, restoredChakra);
                            player.sendStatusMessage(new net.minecraft.util.text.TextComponentString(message), false);
                        }

                        System.out.println("轮回转生生效: " + player.getName() + " 恢复了 " + String.format("%.1f", retentionRatio * 100.0) + "% 查克拉");
                    }
                }

                // 清除所有相关标记
                player.getEntityData().setBoolean("NeedChakraRestore", false);
                player.getEntityData().setBoolean("JustDied", false);
            }
        }
    }
}
