package net.narutomod.item;

import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import net.minecraftforge.fml.client.FMLClientHandler;
import net.minecraftforge.common.util.EnumHelper;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.client.model.ModelLoader;
import net.minecraftforge.client.event.ModelRegistryEvent;
import net.minecraftforge.client.event.MouseEvent;
import net.minecraftforge.event.entity.EntityJoinWorldEvent;
import net.minecraftforge.event.entity.living.LivingAttackEvent;

import net.minecraft.world.World;
import net.minecraft.item.ItemStack;
import net.minecraft.item.ItemArmor;
import net.minecraft.item.Item;
import net.minecraft.inventory.EntityEquipmentSlot;
import net.minecraft.entity.player.EntityPlayerMP;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.client.renderer.block.model.ModelResourceLocation;
import net.minecraft.client.model.ModelBiped;
import net.minecraft.client.util.ITooltipFlag;
import net.minecraft.client.Minecraft;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.text.TextFormatting;

import net.minecraft.util.text.translation.I18n;
import net.minecraft.block.material.Material;

import net.narutomod.procedure.ProcedureSharinganHelmetTickEvent;
import net.narutomod.procedure.ProcedureSync;
import net.narutomod.procedure.ProcedureUtils;
import net.narutomod.procedure.ProcedureOnLivingUpdate;
import net.narutomod.creativetab.TabModTab;
import net.narutomod.ElementsNarutomodMod;
import net.narutomod.yongheng.Pilaozhi;

import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import javax.annotation.Nullable;
import net.minecraft.util.math.RayTraceResult;

@ElementsNarutomodMod.ModElement.Tag
public class ItemSharingan extends ElementsNarutomodMod.ModElement {
	@ObjectHolder("narutomod:sharinganhelmet")
	public static final Item helmet = null;
	
	public ItemSharingan(ElementsNarutomodMod instance) {
		super(instance, 56);
	}

	public static class Base extends ItemDojutsu.Base {
		private boolean canDamage;

		public Base(ItemArmor.ArmorMaterial material) {
			super(material);
		}

		@Override
		public ItemDojutsu.Type getType() {
			return ItemDojutsu.Type.SHARINGAN;
		}

		public Type getSubType() {
			return Type.BASE;
		}

		public boolean isMangekyo() {
			return false;
		}

		public boolean isEternal() {
			return false;
		}

		@SideOnly(Side.CLIENT)
		@Override
		public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
			ItemDojutsu.ClientModel.ModelHelmetSnug armorModel = (ItemDojutsu.ClientModel.ModelHelmetSnug)super.getArmorModel(living, stack, slot, defaultModel);
			armorModel.highlightHide = isBlinded(stack);
			return armorModel;
		}

		@Override
		public void onArmorTick(World world, EntityPlayer entity, ItemStack itemstack) {
			super.onArmorTick(world, entity, itemstack);
			
			// 疲劳值处理 - 戴着时每秒消耗1点疲劳
			if (!world.isRemote && entity.ticksExisted % 20 == 0) {
				Pilaozhi.drainFatigue(entity);
			}
			
			// 检查写轮眼升级 - 只在戴在头上时检测
			if (!world.isRemote && entity.ticksExisted % 20 == 0 && this.isOwner(itemstack, entity) && !this.isMangekyo()) {
				// 先检查勾玉升级
				checkTomoeUpgrade(itemstack, entity);
				
				int tomoeLevel = getTomoeLevel(itemstack, entity);
				
				if (tomoeLevel >= 3) {
					// 三勾玉检查万花筒进化
					net.narutomod.shuxing.AttributeEnhancement attr = 
						net.narutomod.shuxing.AttributeEnhancement.get(entity);
					
					if (attr != null) {
						int eyePowerLevel = attr.getEyePowerLevel();
						int totalLevel = attr.getTotalAllocatedPoints();
						
						// 瞳力值>50 且 查克拉强度[属性表总等级]>100
						if (eyePowerLevel > 50 && totalLevel > 100) {
							// 随机选择万花筒类型
							java.util.List<Item> mangekyoTypes = new java.util.ArrayList<>();
							if (ItemMangekyoSharingan.helmet != null) {
								mangekyoTypes.add(ItemMangekyoSharingan.helmet);
							}
							if (ItemMangekyoSharinganObito.helmet != null) {
								mangekyoTypes.add(ItemMangekyoSharinganObito.helmet);
							}
							if (net.narutomod.item.ItemMangekyoSharinganItachi.helmet != null) {
								mangekyoTypes.add(net.narutomod.item.ItemMangekyoSharinganItachi.helmet);
							}
							if (net.narutomod.item.ItemMangekyoSharinganShisui.helmet != null) {
								mangekyoTypes.add(net.narutomod.item.ItemMangekyoSharinganShisui.helmet);
							}

							if (!mangekyoTypes.isEmpty()) {
								Item selectedMangekyo = mangekyoTypes.get(entity.getRNG().nextInt(mangekyoTypes.size()));
								ItemStack newStack = new ItemStack(selectedMangekyo);

								// 直接替换成全新的万花筒
								entity.setItemStackToSlot(EntityEquipmentSlot.HEAD, newStack);

								entity.sendMessage(new net.minecraft.util.text.TextComponentString(
									net.minecraft.util.text.TextFormatting.RED + "您的写轮眼进化为万花筒写轮眼！"));
							}
						}
					}
				}
			}
			
			if (!world.isRemote && entity.ticksExisted % 6 == 1
			 && (!((Base)itemstack.getItem()).isEternal() || !this.isOwner(itemstack, entity))
			 && (entity.getEntityData().getBoolean("amaterasu_active")
			  || entity.getEntityData().getBoolean("susanoo_activated") || entity.getEntityData().getBoolean("kamui_teleport"))) {
				((Base)itemstack.getItem()).canDamage = true;
				itemstack.damageItem(this.isOwner(itemstack, entity) ? 3 : 9, entity);
				((Base)itemstack.getItem()).canDamage = false;
			}
		}

		@Override
		public void onUpdate(ItemStack itemstack, World world, Entity entity, int par4, boolean par5) {
			super.onUpdate(itemstack, world, entity, par4, par5);
			
			// 移除所有升级逻辑，只保留疲劳损伤
			if (!world.isRemote && entity.ticksExisted % 6 == 1
			 && (!((Base)itemstack.getItem()).isEternal() || !this.isOwner(itemstack, (EntityLivingBase)entity))
			 && (entity.getEntityData().getBoolean("amaterasu_active")
			  || entity.getEntityData().getBoolean("susanoo_activated") || entity.getEntityData().getBoolean("kamui_teleport"))) {
				
				// 应用瞳力强化减少疲劳
				float baseDamage = this.isOwner(itemstack, (EntityLivingBase)entity) ? 3.0f : 9.0f;
				if (entity instanceof EntityPlayer) {
					float reducedDamage = net.narutomod.shuxing.AttributeManager.applyEyeFatigueReduction(
						(EntityPlayer)entity, baseDamage);
					baseDamage = Math.max(1.0f, reducedDamage);
				}
				
				((Base)itemstack.getItem()).canDamage = true;
				itemstack.damageItem((int)baseDamage, (EntityLivingBase)entity);
				((Base)itemstack.getItem()).canDamage = false;
			}
		}

		// returns true if evaded, false if otherwise
		public boolean onAttackEvent(LivingAttackEvent event, EntityLivingBase entity, EntityLivingBase attacker) {
			float evadeChance = getEvadeChance(entity.getItemStackFromSlot(EntityEquipmentSlot.HEAD), entity);
			
			if (entity.getRNG().nextFloat() <= evadeChance) {
				Entity immediateSource = event.getSource().getImmediateSource();
				List<BlockPos> list = ProcedureUtils.getAllAirBlocks(entity.world, entity.getEntityBoundingBox().grow(2.5d));
				for (int i = 0; i < list.size(); i++) {
					BlockPos pos = list.get(entity.getRNG().nextInt(list.size()));
					Material material = entity.world.getBlockState(pos.down()).getMaterial();
					if ((material.isSolid() || material == material.WATER)
					 && immediateSource.getDistanceSqToCenter(pos) > 6.25d && ProcedureUtils.isSpaceOpenToStandOn(entity, pos)) {
						event.setCanceled(true);
						entity.setPositionAndUpdate(0.5d+pos.getX(), pos.getY(), 0.5d+pos.getZ());
						return true;
					}
				}
			}
			return false;
		}

		@Override
		public void setDamage(ItemStack stack, int damage) {
			if (this.canDamage) {
				super.setDamage(stack, damage);
			}
		}

		public void forceDamage(ItemStack stack, int damage) {
			super.setDamage(stack, damage);
		}

		@Override
		public int getDamage(ItemStack stack) {
			int itemDamage = this.getMetadata(stack);
			if (itemDamage > this.getMaxDamage()) {
				itemDamage = this.getMaxDamage();
			}
			return itemDamage;
		}

		@Override
		public void setOwner(ItemStack stack, EntityLivingBase entityIn) {
			super.setOwner(stack, entityIn);
			this.setColor(stack, entityIn.getRNG().nextInt());
		}

		@Override
		public void copyOwner(ItemStack toStack, ItemStack fromStack) {
			super.copyOwner(toStack, fromStack);
			if (toStack.getItem() instanceof Base && fromStack.getItem() instanceof Base) {
				this.setColor(toStack, ((Base)fromStack.getItem()).getColor(fromStack));
			}
		}

		public void setColor(ItemStack stack, int color) {
			if (!stack.hasTagCompound()) {
				stack.setTagCompound(new NBTTagCompound());
			}
			stack.getTagCompound().setInteger("color", (color & 0x00FFFFFF) | 0x20000000);
		}

		public int getColor(ItemStack stack) {
			return stack.hasTagCompound() ? stack.getTagCompound().getInteger("color") : 0;
		}

		@SideOnly(Side.CLIENT)
		@Override
		public void addInformation(ItemStack stack, @Nullable World worldIn, List<String> tooltip, ITooltipFlag flagIn) {
			super.addInformation(stack, worldIn, tooltip, flagIn);
			
			if (worldIn != null && Minecraft.getMinecraft().player != null) {
				EntityPlayer player = Minecraft.getMinecraft().player;
				if (this.isOwner(stack, player)) {
					float evadeChance = getEvadeChance(stack, player) * 100;
					
					tooltip.add(TextFormatting.YELLOW + "闪避概率: " + String.format("%.0f%%", evadeChance));
					
					// 只有基础写轮眼才显示勾玉等级信息
					if (!this.isMangekyo()) {
						int tomoeLevel = getTomoeLevel(stack, player);
						tooltip.add(TextFormatting.RED + "勾玉等级: " + tomoeLevel + "勾玉");
						
						// 显示升级条件
						net.narutomod.shuxing.AttributeEnhancement attr = 
							net.narutomod.shuxing.AttributeEnhancement.get(player);
						
						if (attr != null) {
							int eyePowerLevel = attr.getEyePowerLevel();
							int totalLevel = attr.getTotalAllocatedPoints();
							
							if (tomoeLevel < 3) {
								int nextTomoeRequirement = (tomoeLevel == 1) ? 20 : 30;
								tooltip.add(TextFormatting.GRAY + "升级到" + (tomoeLevel + 1) + "勾玉需要: 瞳力值 " + nextTomoeRequirement);
								tooltip.add(TextFormatting.GRAY + "当前瞳力值: " + eyePowerLevel);
							} else {
								// 三勾玉显示万花筒进化条件
								tooltip.add(TextFormatting.GOLD + "万花筒进化条件:");
								tooltip.add(TextFormatting.GRAY + "瞳力值 > 50 (当前: " + eyePowerLevel + ")");
								tooltip.add(TextFormatting.GRAY + "查克拉强度 > 100 (当前: " + totalLevel + ")");
							}
						}
					}
				}
			}
			
			tooltip.add(TextFormatting.DARK_GRAY + I18n.translateToLocal("tooltip.sharingan.descr") + TextFormatting.WHITE);
		}

		// 获取写轮眼勾玉等级 - 纯粹的等级获取，不做升级
		public int getTomoeLevel(ItemStack stack, EntityLivingBase entity) {
			if (!this.isOwner(stack, entity) || !(entity instanceof EntityPlayer)) {
				return 1; // 非拥有者默认1勾玉
			}

			// 确保NBT存在
			if (!stack.hasTagCompound()) {
				stack.setTagCompound(new NBTTagCompound());
			}

			NBTTagCompound nbt = stack.getTagCompound();
			if (nbt == null) {
				// 如果仍然为null，创建新的NBT并设置默认值
				nbt = new NBTTagCompound();
				nbt.setInteger("tomoe_level", 1);
				stack.setTagCompound(nbt);
				return 1;
			}

			// 检查是否存在tomoe_level键，如果不存在则设置为1并返回1
			if (!nbt.hasKey("tomoe_level")) {
				nbt.setInteger("tomoe_level", 1);
				return 1;
			}

			// 获取存储的勾玉等级，确保至少为1
			int tomoeLevel = nbt.getInteger("tomoe_level");
			if (tomoeLevel < 1) {
				tomoeLevel = 1;
				nbt.setInteger("tomoe_level", 1);
			}

			return tomoeLevel;
		}

		// 检查并升级勾玉等级 - 只在戴着时调用
		private void checkTomoeUpgrade(ItemStack stack, EntityPlayer player) {
			if (!this.isOwner(stack, player)) {
				return;
			}
			
			net.narutomod.shuxing.AttributeEnhancement attr = 
				net.narutomod.shuxing.AttributeEnhancement.get(player);
			
			if (attr != null) {
				int eyePowerLevel = attr.getEyePowerLevel();
				int currentTomoe = getTomoeLevel(stack, player);
				int newTomoe = currentTomoe;
				
				if (eyePowerLevel >= 30 && currentTomoe < 3) {
					newTomoe = 3; // 升级到3勾玉
				} else if (eyePowerLevel >= 20 && currentTomoe < 2) {
					newTomoe = 2; // 升级到2勾玉
				}
				
				// 如果等级有变化，更新NBT
				if (newTomoe != currentTomoe) {
					if (!stack.hasTagCompound()) {
						stack.setTagCompound(new NBTTagCompound());
					}
					NBTTagCompound nbt = stack.getTagCompound();
					if (nbt != null) {
						nbt.setInteger("tomoe_level", newTomoe);
					}
				}
			}
		}

		// 根据勾玉等级获取闪避概率
		public float getEvadeChance(ItemStack stack, EntityLivingBase entity) {
			if (this.isEternal()) {
				return 0.4f; // 永恒万花筒40%
			}
			if (this.isMangekyo()) {
				return 0.3f; // 万花筒30%
			}
			
			int tomoeLevel = getTomoeLevel(stack, entity);
			switch (tomoeLevel) {
				case 1: return 0.1f;  // 10%
				case 2: return 0.2f;  // 20%
				case 3: return 0.25f; // 25%
				default: return 0.1f;
			}
		}
	}

	public static boolean hasAny(EntityPlayer player) {
		return ProcedureUtils.hasAnyItemOfSubtype(player, Base.class);
	}

	public static boolean hasAnyMangekyo(EntityPlayer player, boolean checkIsOwner) {
		for (ItemStack stack : ProcedureUtils.getAllItemsOfSubType(player, Base.class)) {
			if ((!checkIsOwner || ((Base)stack.getItem()).isOwner(stack, player)) && ((Base)stack.getItem()).isMangekyo()) {
				return true;
			}
		}
		return false;
	}

	public static boolean wearingAny(EntityLivingBase entity) {
		return entity.getItemStackFromSlot(EntityEquipmentSlot.HEAD).getItem() instanceof Base;
	}

	public static boolean isMangekyo(ItemStack stack) {
		return stack.getItem() instanceof Base && ((Base)stack.getItem()).isMangekyo();
	}

	public static boolean isWearingMangekyo(EntityLivingBase entity) {
		return isMangekyo(entity.getItemStackFromSlot(EntityEquipmentSlot.HEAD));
	}

	public static boolean isBlinded(ItemStack stack) {
		return stack.hasTagCompound() ? stack.getTagCompound().getBoolean("sharingan_blinded") : false;
	}

	public static boolean isBlinded(EntityPlayer entity) {
		if (entity.isCreative()) {
			return false;
		}
		int i = 0;
		List<ItemStack> list = ProcedureUtils.getAllItemsOfSubType(entity, ItemDojutsu.Base.class);
		for (ItemStack stack : list) {
			if (isBlinded(stack)) {
				++i;
			}
		}
		return !list.isEmpty() && i == list.size();
	}

	public class PlayerHook {
		private static final String shouldTargetLockOnEntity = "shouldTargetLockOnEntity";
		private static final String targetLockOnEntityId = "targetLockOnEntityId";
		private static final String targetLockOnEntityTicksRemaining = "targetLockOnEntityTicksRemaining";

		@SubscribeEvent
		public void onAttacked(LivingAttackEvent event) {
			EntityLivingBase entity = event.getEntityLiving();
			Entity attacker = event.getSource().getTrueSource();
			if (wearingAny(entity) && ItemJutsu.canTarget(entity) && !entity.isRiding() && !event.getSource().isUnblockable()
			 && attacker instanceof EntityLivingBase && !attacker.world.isRemote) {
				((Base)entity.getItemStackFromSlot(EntityEquipmentSlot.HEAD).getItem()).onAttackEvent(event, entity, (EntityLivingBase)attacker);
				if (entity instanceof EntityPlayer) {
					this.lockOnTarget(entity, (EntityLivingBase)attacker, 300);
				}
			}
		}

		@SubscribeEvent
		public void onPlayerTick(TickEvent.PlayerTickEvent event) {
			EntityPlayer entity = event.player;
			if (event.phase == TickEvent.Phase.END) {
				// 疲劳值恢复 - 只有在没戴任何瞳术时才恢复
				if (!entity.world.isRemote && entity.ticksExisted % 20 == 0) {
					if (!wearingAny(entity) && !net.narutomod.item.ItemByakugan.wearingAny(entity)) {
						Pilaozhi.recoverFatigue(entity);
					}
				}
				
				// 原有的目标锁定逻辑
				if (this.hasTargetLockOnEntity(entity)) {
					int remaining = this.targetLockTicksRemaining(entity);
					EntityLivingBase target = this.getLockedTarget(entity);
					if (!entity.world.isRemote && (remaining <= 0 || target == null || !target.isEntityAlive() || target.getDistanceSq(entity) > 1024d)) {
						this.unlockOnTarget(entity);
					} else if (target != null) {
						if (entity.world.isRemote) {
							ProcedureOnLivingUpdate.setGlowingFor(target, 3);
						}
						if (this.shouldLockOnTarget(entity)) {
							RayTraceResult rtr = ProcedureUtils.objectEntityLookingAt(entity, 32d);
							if (rtr == null || rtr.entityHit != target) {
								Vec3d vec2 = target.getPositionEyes(1f).subtract(entity.getPositionEyes(1f));
								entity.rotationYaw = ProcedureUtils.getYawFromVec(vec2);
								entity.rotationPitch = ProcedureUtils.getPitchFromVec(vec2);
							}
						}
						this.lockOnTarget(entity, target, remaining - 1);
					}
				}
			}
		}

		@SideOnly(Side.CLIENT)
		@SubscribeEvent
		public void onMouseEvent(MouseEvent event) {
			EntityPlayer player = Minecraft.getMinecraft().player;
			if (FMLClientHandler.instance().isGUIOpen(net.minecraft.client.gui.GuiChat.class) || player == null) {
				return;
			}
			if (event.getButton() == 1 && this.hasTargetLockOnEntity(player)) {
				//boolean flag = player.getEntityData().getBoolean("shouldTargetLockOnEntity");
				boolean flag = !event.isButtonstate();
				player.getEntityData().setBoolean(shouldTargetLockOnEntity, !flag);
				ProcedureSync.EntityNBTTag.sendToServer(player, shouldTargetLockOnEntity, !flag);
			}
		}

		@SubscribeEvent
		public void onEntitySpawn(EntityJoinWorldEvent event) {
			if (event.getEntity() instanceof EntityPlayerMP) {
				this.unlockOnTarget((EntityLivingBase)event.getEntity());
			}
		}

		private void lockOnTarget(EntityLivingBase entity, EntityLivingBase target, int ticks) {
			if (!entity.world.isRemote) {
				entity.getEntityData().setInteger(targetLockOnEntityId, target.getEntityId());
				entity.getEntityData().setInteger(targetLockOnEntityTicksRemaining, ticks);
				if (entity instanceof EntityPlayerMP) {
					ProcedureSync.EntityNBTTag.sendToSelf((EntityPlayerMP)entity, targetLockOnEntityId, target.getEntityId());
				}
			}
		}
	
		private void unlockOnTarget(EntityLivingBase entity) {
			if (!entity.world.isRemote) {
				entity.getEntityData().removeTag(targetLockOnEntityId);
				entity.getEntityData().removeTag(targetLockOnEntityTicksRemaining);
				entity.getEntityData().removeTag(shouldTargetLockOnEntity);
				if (entity instanceof EntityPlayerMP) {
					ProcedureSync.EntityNBTTag.sendToSelf((EntityPlayerMP)entity, targetLockOnEntityId);
					ProcedureSync.EntityNBTTag.sendToSelf((EntityPlayerMP)entity, shouldTargetLockOnEntity);
				}
			}
		}

		private boolean shouldLockOnTarget(EntityLivingBase entity) {
			return entity.getEntityData().getBoolean(shouldTargetLockOnEntity);
		}
	
		private boolean hasTargetLockOnEntity(EntityLivingBase entity) {
			return entity.getEntityData().hasKey(targetLockOnEntityId);
		}
	
		@Nullable
		private EntityLivingBase getLockedTarget(EntityLivingBase entity) {
			Entity target = entity.world.getEntityByID(entity.getEntityData().getInteger(targetLockOnEntityId));
			return target instanceof EntityLivingBase ? (EntityLivingBase)target : null;
		}
	
		private int targetLockTicksRemaining(EntityLivingBase entity) {
			return entity.getEntityData().getInteger(targetLockOnEntityTicksRemaining);
		}
	}

	@Override
	public void initElements() {
		ItemArmor.ArmorMaterial enuma = EnumHelper.addArmorMaterial("SHARINGAN", "narutomod:sharingan_",
		 1024, new int[]{2, 5, 6, 10}, 0, null, 0.0F);
		this.elements.items.add(() -> new Base(enuma) {
			public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
				if (entity instanceof EntityLivingBase) {
					int tomoeLevel = getTomoeLevel(stack, (EntityLivingBase)entity);
					switch (tomoeLevel) {
						case 1: return "narutomod:textures/sharinganhelmet1.png";
						case 2: return "narutomod:textures/sharinganhelmet2.png";
						case 3: return "narutomod:textures/sharinganhelmet.png";
						default: return "narutomod:textures/sharinganhelmet1.png";
					}
				}
				return "narutomod:textures/sharinganhelmet1.png";
			}
		}.setUnlocalizedName("sharinganhelmet").setRegistryName("sharinganhelmet").setCreativeTab(TabModTab.tab));
	}

	@Override
	@SideOnly(Side.CLIENT)
	public void registerModels(ModelRegistryEvent event) {
		ModelLoader.setCustomModelResourceLocation(helmet, 0, new ModelResourceLocation("narutomod:sharinganhelmet", "inventory"));
	}

	@Override
	public void init(FMLInitializationEvent event) {
		MinecraftForge.EVENT_BUS.register(new PlayerHook());
	}

	public enum Type {
		BASE,
		AMATERASU,
		KAMUI;
	}

	// 查找物品在背包中的槽位 - 改为静态方法
	private static int findSlotId(EntityPlayer player, ItemStack stack) {
		for (int i = 0; i < player.inventory.getSizeInventory(); i++) {
			if (player.inventory.getStackInSlot(i) == stack) {
				return i;
			}
		}
		return -1;
	}


}

